"""
状态栏组件
"""
import tkinter as tk
from tkinter import ttk
import time
import threading


class StatusBar:
    """状态栏组件"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.progress_active = False
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面组件"""
        self.frame = ttk.Frame(self.parent)
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(self.frame, textvariable=self.status_var, 
                                     anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 进度条
        self.progress = ttk.Progressbar(self.frame, mode="indeterminate", length=100)
        self.progress.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 设置默认状态
        self.set_status("就绪", "INFO")
    
    def set_status(self, message: str, level: str = "INFO"):
        """设置状态信息"""
        self.status_var.set(message)
        
        # 根据级别设置颜色
        if level == "ERROR":
            self.status_label.config(foreground="#d93025")  # 红色
        elif level == "WARNING":
            self.status_label.config(foreground="#ea8600")  # 橙色
        elif level == "SUCCESS":
            self.status_label.config(foreground="#1e8e3e")  # 绿色
        else:
            self.status_label.config(foreground="#202124")  # 黑色
        
        # 根据级别控制进度条
        if level in ["INFO", "WARNING"] and "正在" in message:
            self.start_progress()
        elif level in ["SUCCESS", "ERROR"] or "完成" in message:
            self.stop_progress()
    
    def start_progress(self):
        """启动进度条"""
        if not self.progress_active:
            self.progress.start(10)
            self.progress_active = True
    
    def stop_progress(self):
        """停止进度条"""
        if self.progress_active:
            self.progress.stop()
            self.progress_active = False
    
    def show_temporary_status(self, message: str, level: str = "INFO", duration: int = 3000):
        """显示临时状态信息"""
        original_message = self.status_var.get()
        original_level = "INFO"  # 默认级别
        
        # 尝试从当前颜色判断原始级别
        fg_color = self.status_label.cget("foreground")
        if fg_color == "#d93025":
            original_level = "ERROR"
        elif fg_color == "#ea8600":
            original_level = "WARNING"
        elif fg_color == "#1e8e3e":
            original_level = "SUCCESS"
        
        # 设置新状态
        self.set_status(message, level)
        
        # 创建定时器恢复原始状态
        def _restore():
            time.sleep(duration / 1000)
            
            # 在主线程中更新UI
            self.frame.after(0, lambda: self.set_status(original_message, original_level))
        
        # 启动后台线程
        threading.Thread(target=_restore, daemon=True).start()