"""
简化版邮箱生成面板
"""
import tkinter as tk
from tkinter import ttk, messagebox
import pyperclip
from typing import List
from src.models import EmailInfo
from src.gui.theme_manager import theme_manager
from src.gui.custom_widgets import ModernButton, StyledTreeview
from src.gui.layout_manager import LayoutManager
# from src.user_management.email_storage_manager import EmailStorageManager  # 暂时注释掉用户相关功能


class SimpleEmailPanel:
    """简化版邮箱生成面板"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.emails = []
        self._is_compact_mode = False

        # # 初始化邮箱存储管理器 - 暂时注释掉用户相关功能
        # self.email_storage = EmailStorageManager()

        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面组件"""
        self.frame = ttk.Frame(self.parent)
        self.frame.configure(style='TFrame')
        
        # 创建控制面板
        self.control_frame = ttk.Frame(self.frame)
        self.control_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # 创建左侧控制区域
        self.left_control = ttk.Frame(self.control_frame)
        self.left_control.pack(side=tk.LEFT)
        
        # 生成数量选择
        # self.count_frame = ttk.Frame(self.left_control)
        # self.count_frame.pack(side=tk.LEFT, padx=(0, 15))
        
        # self.count_label = ttk.Label(self.count_frame, text="生成数量:")
        # self.count_label.pack(side=tk.LEFT)
        
        self.count_var = tk.StringVar(value="1")
        # self.count_combo = ttk.Combobox(self.count_frame, textvariable=self.count_var, 
        #                                values=["1", "5", "10", "20"], 
        #                                width=8, state="readonly")
        # self.count_combo.pack(side=tk.LEFT, padx=5)
        
        # 创建按钮组
        self.generate_btn = ModernButton(self.control_frame, text="生成邮箱", 
                                        command=self._on_generate_click)
        
        self.clear_btn = ModernButton(self.control_frame, text="清除列表", 
                                     style='Secondary.TButton',
                                     command=self._on_clear_click)
        
        # 使用布局管理器创建按钮组
        self.buttons = [self.generate_btn, self.clear_btn]
        self.button_group = LayoutManager.create_button_group(self.control_frame, self.buttons)
        self.button_group.pack(side=tk.RIGHT)
        
        # 创建邮箱列表区域
        self.list_frame = LayoutManager.create_section_frame(self.frame, "生成的邮箱地址")
        
        # 创建树形视图容器
        self.tree_container = ttk.Frame(self.list_frame)
        self.tree_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建树形视图
        self.columns = ("序号", "邮箱地址", "生成时间")
        self.tree = StyledTreeview(self.tree_container, columns=self.columns, show="headings", height=12)
        
        # 设置列标题和宽度
        self.tree.heading("序号", text="序号")
        self.tree.heading("邮箱地址", text="邮箱地址")
        self.tree.heading("生成时间", text="生成时间")
        
        # 初始列宽设置
        self._set_column_widths()
        
        # 添加滚动条
        self.scrollbar = ttk.Scrollbar(self.tree_container, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=self.scrollbar.set)
        
        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.tree.bind("<Double-1>", self._on_item_double_click)
        
        # 右键菜单
        self.context_menu = tk.Menu(self.tree, tearoff=0)
        self.context_menu.add_command(label="复制邮箱", command=self._copy_selected_email)
        self.context_menu.add_command(label="复制所有邮箱", command=self._copy_all_emails)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除选中", command=self._delete_selected)
        
        self.tree.bind("<Button-3>", self._show_context_menu)
        
        # 底部信息区域
        info_frame = ttk.Frame(self.frame)
        info_frame.pack(fill=tk.X, padx=20, pady=(5, 15))
        
        self.info_label = ttk.Label(info_frame, text="提示: 双击邮箱地址可复制到剪贴板",
                                  style='Small.TLabel')
        self.info_label.pack(side=tk.LEFT)
        
        self.count_label = ttk.Label(info_frame, text="总计: 0 个邮箱")
        self.count_label.pack(side=tk.RIGHT)
    
    def _on_generate_click(self):
        """生成按钮点击事件"""
        try:
            count = int(self.count_var.get())
            if count < 1 or count > 50:  # 简化版限制最大数量
                messagebox.showerror("错误", "生成数量必须在1-50之间")
                return
            
            self.main_window.generate_email(count)
            
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数量")
    
    def _on_clear_click(self):
        """清除按钮点击事件"""
        if self.emails:
            result = messagebox.askyesno("确认", "确定要清除所有邮箱吗？")
            if result:
                self.clear_emails()
    
    def _on_item_double_click(self, event):
        """列表项双击事件"""
        self._copy_selected_email()
    
    def _show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.post(event.x_root, event.y_root)
        except:
            pass
    
    def _copy_selected_email(self):
        """复制选中的邮箱"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一个邮箱")
            return
        
        item = self.tree.item(selection[0])
        email = item['values'][1]  # 邮箱地址在第二列
        
        try:
            pyperclip.copy(email)
            self.main_window.app.log_manager.add_log(f"已复制邮箱: {email}", "SUCCESS")
            
            # 显示复制成功的视觉反馈
            self.main_window.status_bar.set_status(f"邮箱 {email} 已复制到剪贴板", "SUCCESS")
        except Exception as e:
            messagebox.showerror("错误", f"复制失败: {str(e)}")
    
    def _copy_all_emails(self):
        """复制所有邮箱"""
        if not self.emails:
            messagebox.showwarning("提示", "没有邮箱可复制")
            return
        
        email_list = [email.address for email in self.emails]
        email_text = "\n".join(email_list)
        
        try:
            pyperclip.copy(email_text)
            self.main_window.app.log_manager.add_log(f"已复制{len(email_list)}个邮箱", "SUCCESS")
            messagebox.showinfo("成功", f"已复制{len(email_list)}个邮箱到剪贴板")
        except Exception as e:
            messagebox.showerror("错误", f"复制失败: {str(e)}")
    
    def _delete_selected(self):
        """删除选中的邮箱"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要删除的邮箱")
            return
        
        result = messagebox.askyesno("确认", "确定要删除选中的邮箱吗？")
        if result:
            # 获取选中项的索引
            for item_id in selection:
                item = self.tree.item(item_id)
                index = int(item['values'][0]) - 1  # 序号从1开始，索引从0开始
                
                # 从列表中删除
                if 0 <= index < len(self.emails):
                    del self.emails[index]
            
            # 刷新显示
            self._refresh_tree()
    
    def add_emails(self, emails: List[EmailInfo]):
        """添加邮箱到列表"""
        self.emails.extend(emails)
        self._refresh_tree()

        # # 自动保存到本地存储 - 暂时注释掉用户相关功能
        # self._save_emails_to_storage()
    
    def clear_emails(self):
        """清除所有邮箱"""
        self.emails.clear()
        self._refresh_tree()
        self.main_window.app.log_manager.add_log("已清除所有邮箱", "INFO")

        # # 清除本地存储 - 暂时注释掉用户相关功能
        # self._clear_emails_from_storage()
    
    def _refresh_tree(self):
        """刷新树形视图"""
        # 清除现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加新项目
        for i, email in enumerate(self.emails, 1):
            time_str = email.generated_time.strftime("%Y-%m-%d %H:%M:%S")
            tag = 'even' if i % 2 == 0 else 'odd'
            self.tree.insert("", tk.END, values=(i, email.address, time_str), tags=(tag,))
        
        # 更新计数标签
        self.count_label.config(text=f"总计: {len(self.emails)} 个邮箱")

    # def load_user_emails(self, user_id: str):
    #     """加载用户的邮箱数据 - 暂时注释掉用户相关功能"""
    #     if not user_id:
    #         return

    #     try:
    #         # 从存储中加载邮箱
    #         stored_emails = self.email_storage.load_user_emails(user_id)

    #         if stored_emails:
    #             self.emails = stored_emails
    #             self._refresh_tree()
    #             self.main_window.app.log_manager.add_log(f"已加载 {len(stored_emails)} 个历史邮箱", "SUCCESS")
    #             print(f"为用户 {user_id} 加载了 {len(stored_emails)} 个邮箱")
    #         else:
    #             print(f"用户 {user_id} 没有历史邮箱数据")

    #     except Exception as e:
    #         print(f"加载用户邮箱失败: {e}")
    #         self.main_window.app.log_manager.add_log(f"加载历史邮箱失败: {str(e)}", "ERROR")

    # def _save_emails_to_storage(self):
    #     """保存邮箱到本地存储 - 暂时注释掉用户相关功能"""
    #     current_user = self.main_window.permission_manager.current_user
    #     if not current_user:
    #         return

    #     try:
    #         self.email_storage.save_user_emails(current_user.user_id, self.emails)
    #     except Exception as e:
    #         print(f"保存邮箱到存储失败: {e}")

    # def _clear_emails_from_storage(self):
    #     """清除本地存储的邮箱 - 暂时注释掉用户相关功能"""
    #     current_user = self.main_window.permission_manager.current_user
    #     if not current_user:
    #         return

    #     try:
    #         self.email_storage.clear_user_emails(current_user.user_id)
    #     except Exception as e:
    #         print(f"清除存储邮箱失败: {e}")
    
    def set_compact_mode(self, is_compact):
        """设置紧凑模式"""
        if self._is_compact_mode == is_compact:
            return
            
        self._is_compact_mode = is_compact
        
        if is_compact:
            self._apply_compact_layout()
        else:
            self._apply_normal_layout()
    
    def _apply_compact_layout(self):
        """应用紧凑布局"""
        # 减少内边距
        self.control_frame.pack_configure(padx=10, pady=10)
        self.list_frame.pack_configure(padx=10, pady=5)
        
        # 调整按钮文本为简短版本
        self.generate_btn.configure(text="生成")
        self.clear_btn.configure(text="清除")
        
        # 调整树形视图高度
        self.tree.configure(height=8)
        
        # 调整列宽
        self._set_column_widths(compact=True)
    
    def _apply_normal_layout(self):
        """应用正常布局"""
        # 恢复内边距
        self.control_frame.pack_configure(padx=20, pady=20)
        self.list_frame.pack_configure(padx=10, pady=10)
        
        # 恢复按钮文本
        self.generate_btn.configure(text="生成邮箱")
        self.clear_btn.configure(text="清除列表")
        
        # 恢复树形视图高度
        self.tree.configure(height=12)
        
        # 恢复列宽
        self._set_column_widths(compact=False)
    
    def _set_column_widths(self, compact=False):
        """设置列宽"""
        if compact:
            # 紧凑模式下的列宽
            self.tree.column("序号", width=40, anchor=tk.CENTER)
            self.tree.column("邮箱地址", width=200, anchor=tk.W)
            self.tree.column("生成时间", width=100, anchor=tk.CENTER)
        else:
            # 正常模式下的列宽
            self.tree.column("序号", width=50, anchor=tk.CENTER)
            self.tree.column("邮箱地址", width=300, anchor=tk.W)
            self.tree.column("生成时间", width=130, anchor=tk.CENTER)
