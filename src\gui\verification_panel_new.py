"""
验证码获取面板 (美化版)
"""
import tkinter as tk
from tkinter import ttk, messagebox
import pyperclip
from datetime import datetime
from src.gui.theme_manager import theme_manager
from src.gui.custom_widgets import ModernButton, VerificationCodeDisplay, StyledTreeview
from src.gui.layout_manager import LayoutManager


class VerificationPanel:
    """验证码获取面板"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.current_code = None
        self.code_history = []
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面组件"""
        self.frame = ttk.Frame(self.parent)
        self.frame.configure(style='TFrame')
        
        # 创建标题区域
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = ttk.Label(title_frame, text="验证码获取", 
                               style='Title.TLabel')
        title_label.pack(anchor='center')
        
        # 创建说明文本区域
        info_frame = ttk.Frame(self.frame)
        info_frame.pack(fill=tk.X, padx=20, pady=(0, 15))
        
        info_text = ("此功能从tempmail.plus获取最新邮件中的验证码。\n"
                    "请确保已在配置面板正确设置tempmail.plus的连接参数。")
        info_label = ttk.Label(info_frame, text=info_text, 
                              justify=tk.CENTER,
                              wraplength=600)
        info_label.pack(anchor='center')
        
        # 创建控制按钮区域
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        # 创建按钮
        self.get_code_btn = ModernButton(control_frame, text="获取验证码", 
                                       command=self._on_get_code_click)
        
        test_btn = ModernButton(control_frame, text="测试连接", 
                              style='Secondary.TButton',
                              command=self._on_test_connection_click)
        
        auto_reg_btn = ModernButton(control_frame, text="自动注册流程", 
                                  style='Success.TButton',
                                  command=self._on_auto_registration_click)
        
        # 使用布局管理器创建按钮组
        buttons = [self.get_code_btn, test_btn, auto_reg_btn]
        button_group = LayoutManager.create_button_group(control_frame, buttons)
        button_group.pack(anchor='center')
        
        # 创建验证码显示区域
        self.code_display = VerificationCodeDisplay(self.frame)
        self.code_display.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        # 创建历史记录区域
        history_frame = LayoutManager.create_section_frame(self.frame, "验证码历史")
        
        # 创建历史记录表格
        columns = ("验证码", "获取时间")
        self.history_tree = StyledTreeview(history_frame, columns=columns, 
                                         show="headings", height=8)
        
        # 设置列标题和宽度
        self.history_tree.heading("验证码", text="验证码")
        self.history_tree.heading("获取时间", text="获取时间")
        self.history_tree.column("验证码", width=150, anchor=tk.CENTER)
        self.history_tree.column("获取时间", width=200, anchor=tk.CENTER)
        
        # 添加滚动条
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, 
                                        command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        
        # 布局
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, 
                             padx=(10, 0), pady=10)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)
        
        # 绑定双击事件
        self.history_tree.bind("<Double-1>", self._on_history_double_click)
        
        # 右键菜单
        self.history_menu = tk.Menu(self.history_tree, tearoff=0)
        self.history_menu.add_command(label="复制验证码", command=self._copy_selected_history)
        self.history_menu.add_command(label="清除历史", command=self._clear_history)
        
        self.history_tree.bind("<Button-3>", self._show_history_menu)
        
        # 底部提示
        tip_frame = ttk.Frame(self.frame)
        tip_frame.pack(fill=tk.X, padx=20, pady=(5, 15))
        
        tip_label = ttk.Label(tip_frame, 
                            text="提示: 双击历史记录中的验证码可复制到剪贴板",
                            style='Small.TLabel')
        tip_label.pack(anchor='center')
    
    def _on_get_code_click(self):
        """获取验证码按钮点击事件"""
        self.get_code_btn.config(state=tk.DISABLED, text="获取中...")
        
        def _reset_button():
            self.get_code_btn.config(state=tk.NORMAL, text="获取验证码")
        
        # 延迟重置按钮状态
        self.frame.after(3000, _reset_button)
        
        # 调用主窗口的获取验证码方法
        self.main_window.get_verification_code()
    
    def _on_auto_registration_click(self):
        """自动注册流程按钮点击事件"""
        if not self.main_window.app.workflow_manager:
            messagebox.showerror("错误", "工作流程管理器未初始化，请重启应用")
            return
            
        result = messagebox.askyesno("确认", "是否启动自动注册流程？\n\n这将生成一个新邮箱，并准备获取验证码。")
        if result:
            def on_email_generated(email_info):
                # 切换到邮箱生成面板显示生成的邮箱
                self.main_window.notebook.select(0)  # 选择邮箱生成标签页
                
                # 显示提示信息
                msg = "邮箱已生成: " + email_info.address + "\n\n请使用此邮箱在目标网站注册，然后返回此应用点击获取验证码按钮。"
                messagebox.showinfo("自动注册流程", msg)
            
            # 启动自动注册工作流程
            self.main_window.app.workflow_manager.run_auto_registration_workflow(on_email_generated)
    
    def _on_test_connection_click(self):
        """测试连接按钮点击事件"""
        if not self.main_window.app.tempmail_service:
            messagebox.showerror("错误", "临时邮箱服务未初始化，请先配置应用")
            return
        
        def _test():
            try:
                self.main_window.status_bar.set_status("正在测试连接...", "INFO")
                
                if self.main_window.app.tempmail_service.test_connection():
                    self.main_window.app.log_manager.add_log("连接测试成功", "SUCCESS")
                    messagebox.showinfo("成功", "连接测试成功！")
                else:
                    self.main_window.app.log_manager.add_log("连接测试失败", "ERROR")
                    messagebox.showerror("失败", "连接测试失败，请检查配置和网络连接")
                
                self.main_window.status_bar.set_status("连接测试完成", "INFO")
                
            except Exception as e:
                self.main_window.app.log_manager.add_log(f"连接测试出错: {str(e)}", "ERROR")
                messagebox.showerror("错误", f"连接测试出错: {str(e)}")
        
        # 在后台线程中执行
        import threading
        threading.Thread(target=_test, daemon=True).start()
    
    def _on_history_double_click(self, event):
        """历史记录双击事件"""
        self._copy_selected_history()
    
    def _show_history_menu(self, event):
        """显示历史记录右键菜单"""
        try:
            self.history_menu.post(event.x_root, event.y_root)
        except:
            pass
    
    def _copy_selected_history(self):
        """复制选中的历史验证码"""
        selection = self.history_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一个验证码")
            return
        
        item = self.history_tree.item(selection[0])
        code = item['values'][0]  # 验证码在第一列
        
        try:
            pyperclip.copy(code)
            self.main_window.app.log_manager.add_log(f"已复制历史验证码: {code}", "SUCCESS")
            messagebox.showinfo("成功", f"验证码已复制到剪贴板: {code}")
        except Exception as e:
            messagebox.showerror("错误", f"复制失败: {str(e)}")
    
    def _clear_history(self):
        """清除历史记录"""
        if self.code_history:
            result = messagebox.askyesno("确认", "确定要清除所有历史记录吗？")
            if result:
                self.code_history.clear()
                self._refresh_history()
                self.main_window.app.log_manager.add_log("已清除验证码历史记录", "INFO")
    
    def set_verification_code(self, code: str):
        """设置当前验证码"""
        self.current_code = code
        self.code_display.set_code(code)
        
        # 添加到历史记录
        self.code_history.append({
            'code': code,
            'time': datetime.now()
        })
        
        # 限制历史记录数量
        if len(self.code_history) > 50:
            self.code_history = self.code_history[-50:]
        
        self._refresh_history()
    
    def _refresh_history(self):
        """刷新历史记录显示"""
        # 清除现有项目
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        # 添加历史记录（倒序显示，最新的在前面）
        for record in reversed(self.code_history):
            time_str = record['time'].strftime("%Y-%m-%d %H:%M:%S")
            self.history_tree.insert("", tk.END, values=(record['code'], time_str))