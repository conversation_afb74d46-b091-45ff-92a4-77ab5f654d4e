"""
主题管理器
"""
import tkinter as tk
from tkinter import ttk
import platform
import os


class ThemeManager:
    """主题管理器，负责管理应用程序的视觉风格"""
    
    def __init__(self):
        """初始化主题管理器"""
        # 检测系统和可用字体
        self.system = platform.system()
        self._detect_fonts()
        
        # 定义颜色方案
        self.colors = {
            'primary': '#4a6baf',       # 主色调（深蓝色）
            'primary_light': '#6b8fd1', # 主色调亮色
            'primary_dark': '#2a4b8f',  # 主色调暗色
            'secondary': '#6c757d',     # 次要色调（灰色）
            'success': '#28a745',       # 成功色（绿色）
            'info': '#17a2b8',          # 信息色（青色）
            'warning': '#ffc107',       # 警告色（黄色）
            'danger': '#dc3545',        # 危险色（红色）
            'light': '#f8f9fa',         # 浅色背景
            'dark': '#343a40',          # 深色文本
            'white': '#ffffff',         # 白色
            'border': '#dee2e6',        # 边框颜色
            'hover': '#e9ecef',         # 悬停背景色
            'focus': '#80bdff',         # 焦点边框色
            'disabled': '#e9ecef',      # 禁用状态色
            'bg_light': '#f5f5f5',      # 浅色背景
            'bg_dark': '#e0e0e0',       # 深色背景
            'text_primary': '#212529',  # 主要文本色
            'text_secondary': '#6c757d' # 次要文本色
        }
        
        # 定义字体方案（根据DPI缩放调整大小）- 第三轮大幅增大字体
        base_sizes = {
            'title': 28,        # 22 → 28 (+27%)
            'subtitle': 20,     # 16 → 20 (+25%)
            'body': 16,         # 13 → 16 (+23%)
            'small': 14,        # 12 → 14 (+17%)
            'button': 16,       # 13 → 16 (+23%)
            'code': 15,         # 12 → 15 (+25%)
            'verification_code': 22  # 18 → 22 (+22%)
        }

        # 应用DPI缩放
        self.fonts = {}
        for key, base_size in base_sizes.items():
            # 确保字体足够大，最小字体大小根据类型设定 - 第三轮提升最小限制
            min_sizes = {
                'title': 26, 'subtitle': 18, 'body': 15, 'small': 13,
                'button': 15, 'code': 14, 'verification_code': 20
            }
            min_size = min_sizes.get(key, 14)  # 默认最小字体从11提升到14
            scaled_size = max(min_size, int(base_size * self.dpi_scale))

            if key == 'title':
                self.fonts[key] = (self.system_font, scaled_size, 'bold')
            elif key == 'subtitle':
                self.fonts[key] = (self.system_font, scaled_size, 'bold')
            elif key in ['code', 'verification_code']:
                weight = 'bold' if key == 'verification_code' else 'normal'
                self.fonts[key] = (self.monospace_font, scaled_size, weight)
            elif key == 'button':
                self.fonts[key] = (self.system_font, scaled_size, 'normal')
            else:
                self.fonts[key] = (self.system_font, scaled_size, 'normal')
        
        # 定义样式参数 - 第三轮大幅增加间距适应更大字体
        self.styles = {
            'padding': 18,          # 12 → 18 (+50%)
            'small_padding': 10,    # 6 → 10 (+67%)
            'large_padding': 25,    # 18 → 25 (+39%)
            'border_radius': 6,     # 4 → 6 (+50%)
            'border_width': 1,
            'shadow': '2px 2px 4px #00000033'  # 增强阴影效果
        }
    
    def _detect_fonts(self):
        """检测系统可用字体"""
        # 检测DPI缩放比例
        self.dpi_scale = self._get_dpi_scale()

        if self.system == 'Windows':
            # Windows系统优先使用微软雅黑，更清晰的字体
            self.system_font = 'Microsoft YaHei'  # 使用标准版本而非UI版本
            self.monospace_font = 'Consolas'
        elif self.system == 'Darwin':  # macOS
            self.system_font = 'PingFang SC'
            self.monospace_font = 'Menlo'
        else:  # Linux和其他系统
            self.system_font = 'Noto Sans CJK SC'
            self.monospace_font = 'DejaVu Sans Mono'

        # 如果首选字体不可用，使用备选字体
        self.system_font_fallbacks = ['Microsoft YaHei UI', 'SimHei', 'WenQuanYi Micro Hei', 'Arial Unicode MS', 'Arial']
        self.monospace_font_fallbacks = ['Courier New', 'Liberation Mono', 'Monospace']

        # 验证字体可用性并选择最佳字体
        self.system_font = self._get_best_font([self.system_font] + self.system_font_fallbacks)
        self.monospace_font = self._get_best_font([self.monospace_font] + self.monospace_font_fallbacks)

    def _get_dpi_scale(self):
        """获取DPI缩放比例"""
        try:
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()  # 隐藏窗口

            # 获取DPI
            dpi = root.winfo_fpixels('1i')
            root.destroy()

            # 计算缩放比例（96 DPI为标准）
            scale = dpi / 96.0
            return max(1.0, scale)  # 最小缩放比例为1.0
        except:
            return 1.0  # 默认缩放比例

    def _get_best_font(self, font_list):
        """从字体列表中选择最佳可用字体"""
        try:
            import tkinter as tk
            import tkinter.font as tkfont

            root = tk.Tk()
            root.withdraw()

            available_fonts = tkfont.families()

            for font in font_list:
                if font in available_fonts:
                    root.destroy()
                    return font

            root.destroy()
            return font_list[-1]  # 返回最后一个作为备选
        except:
            return font_list[0]  # 如果检测失败，返回第一个
    
    def apply_theme(self, root):
        """应用主题到根窗口"""
        # 设置高DPI支持
        self._setup_high_dpi_support(root)

        # 配置根窗口
        root.configure(bg=self.colors['bg_light'])

        # 创建ttk样式
        style = ttk.Style()

        # 尝试使用更现代的主题
        available_themes = style.theme_names()
        if 'clam' in available_themes:
            style.theme_use('clam')  # clam主题比较适合自定义
        elif 'vista' in available_themes and self.system == 'Windows':
            style.theme_use('vista')  # Windows Vista主题

        # 配置各种ttk部件的样式
        self._configure_button_style(style)
        self._configure_entry_style(style)
        self._configure_notebook_style(style)
        self._configure_treeview_style(style)
        self._configure_frame_style(style)
        self._configure_label_style(style)
        self._configure_scrollbar_style(style)
        self._configure_combobox_style(style)

        return style

    def _setup_high_dpi_support(self, root):
        """设置高DPI支持"""
        try:
            if self.system == 'Windows':
                # Windows高DPI支持
                try:
                    from ctypes import windll
                    windll.shcore.SetProcessDpiAwareness(1)  # 设置DPI感知
                except:
                    pass

                # 设置tkinter的DPI感知
                try:
                    root.tk.call('tk', 'scaling', self.dpi_scale)
                except:
                    pass

            # 设置字体渲染选项
            root.option_add('*Font', self.fonts['body'])
            root.option_add('*foreground', self.colors['text_primary'])
            root.option_add('*background', self.colors['bg_light'])

        except Exception as e:
            print(f"设置高DPI支持时出错: {e}")
    
    def _configure_button_style(self, style):
        """配置按钮样式"""
        # 默认按钮样式 - 增加内边距适应更大字体
        style.configure('TButton',
                        font=self.fonts['button'],
                        background=self.colors['primary'],
                        foreground=self.colors['white'],
                        padding=(self.styles['padding'], self.styles['padding']),  # 使用更大的垂直内边距
                        borderwidth=0)
        
        style.map('TButton',
                 background=[('active', self.colors['primary_light']),
                             ('disabled', self.colors['disabled'])],
                 foreground=[('disabled', self.colors['secondary'])])
        
        # 次要按钮样式
        style.configure('Secondary.TButton',
                        background=self.colors['secondary'],
                        foreground=self.colors['white'])
        
        style.map('Secondary.TButton',
                 background=[('active', self.colors['secondary'] + 'dd'),
                             ('disabled', self.colors['disabled'])])
        
        # 成功按钮样式
        style.configure('Success.TButton',
                        background=self.colors['success'],
                        foreground=self.colors['white'])
        
        style.map('Success.TButton',
                 background=[('active', self.colors['success'] + 'dd'),
                             ('disabled', self.colors['disabled'])])
        
        # 危险按钮样式
        style.configure('Danger.TButton',
                        background=self.colors['danger'],
                        foreground=self.colors['white'])
        
        style.map('Danger.TButton',
                 background=[('active', self.colors['danger'] + 'dd'),
                             ('disabled', self.colors['disabled'])])
        
        # 链接按钮样式
        style.configure('Link.TButton',
                        background=self.colors['bg_light'],
                        foreground=self.colors['primary'],
                        borderwidth=0)
        
        style.map('Link.TButton',
                 foreground=[('active', self.colors['primary_dark']),
                             ('disabled', self.colors['secondary'])])
    
    def _configure_entry_style(self, style):
        """配置输入框样式"""
        style.configure('TEntry',
                        font=self.fonts['body'],
                        padding=self.styles['padding'],  # 使用更大的内边距
                        fieldbackground=self.colors['white'],
                        borderwidth=1,
                        relief='solid')
        
        style.map('TEntry',
                 bordercolor=[('focus', self.colors['primary']),
                              ('disabled', self.colors['disabled'])])
        
        # 错误状态的输入框
        style.configure('Error.TEntry',
                        bordercolor=self.colors['danger'])
    
    def _configure_notebook_style(self, style):
        """配置选项卡样式"""
        style.configure('TNotebook',
                        background=self.colors['bg_light'],
                        tabmargins=[2, 5, 2, 0])
        
        style.configure('TNotebook.Tab',
                        font=self.fonts['body'],
                        padding=[20, 12],  # 进一步增加选项卡内边距 [15,8] → [20,12]
                        background=self.colors['bg_light'],
                        foreground=self.colors['text_secondary'])
        
        style.map('TNotebook.Tab',
                 background=[('selected', self.colors['white']),
                             ('active', self.colors['hover'])],
                 foreground=[('selected', self.colors['primary']),
                             ('active', self.colors['text_primary'])])

        # 单选按钮样式 - 使用大字体
        style.configure('TRadiobutton',
                        font=self.fonts['body'],  # 使用正文字体 (16px)
                        background=self.colors['white'],  # 使用白色背景
                        foreground=self.colors['text_primary'],
                        focuscolor='none')  # 去除焦点框

        # 大字体单选按钮样式 - 专门用于重要选项
        style.configure('Large.TRadiobutton',
                        font=self.fonts['subtitle'],  # 使用副标题字体 (20px)
                        background=self.colors['white'],  # 使用白色背景
                        foreground=self.colors['text_primary'],
                        focuscolor='none')  # 去除焦点框

        # 复选框样式 - 使用大字体
        style.configure('TCheckbutton',
                        font=self.fonts['body'],  # 使用正文字体 (16px)
                        background=self.colors['white'],  # 使用白色背景
                        foreground=self.colors['text_primary'],
                        focuscolor='none')  # 去除焦点框

    def _configure_treeview_style(self, style):
        """配置树形视图样式"""
        style.configure('Treeview',
                        font=self.fonts['body'],
                        background=self.colors['white'],
                        fieldbackground=self.colors['white'],
                        foreground=self.colors['text_primary'],
                        borderwidth=1,
                        relief='solid')
        
        style.configure('Treeview.Heading',
                        font=self.fonts['subtitle'],
                        background=self.colors['bg_dark'],
                        foreground=self.colors['text_primary'],
                        padding=[12, 10])  # 进一步增加表格标题内边距 [8,6] → [12,10]
        
        style.map('Treeview',
                 background=[('selected', self.colors['primary_light'])],
                 foreground=[('selected', self.colors['white'])])
    
    def _configure_frame_style(self, style):
        """配置框架样式"""
        style.configure('TFrame',
                        background=self.colors['bg_light'])
        
        style.configure('Card.TFrame',
                        background=self.colors['white'],
                        borderwidth=1,
                        relief='solid')
        
        style.configure('TLabelframe',
                        font=self.fonts['subtitle'],
                        background=self.colors['bg_light'],
                        foreground=self.colors['text_primary'])
        
        style.configure('TLabelframe.Label',
                        font=self.fonts['subtitle'],
                        background=self.colors['bg_light'],
                        foreground=self.colors['text_primary'])
    
    def _configure_label_style(self, style):
        """配置标签样式"""
        style.configure('TLabel',
                        font=self.fonts['body'],
                        background=self.colors['bg_light'],
                        foreground=self.colors['text_primary'])
        
        style.configure('Title.TLabel',
                        font=self.fonts['title'],
                        foreground=self.colors['primary'])
        
        style.configure('Subtitle.TLabel',
                        font=self.fonts['subtitle'])
        
        style.configure('Small.TLabel',
                        font=self.fonts['small'],
                        foreground=self.colors['text_secondary'])
        
        style.configure('Code.TLabel',
                        font=self.fonts['code'],
                        background=self.colors['bg_dark'],
                        padding=self.styles['small_padding'])
        
        style.configure('VerificationCode.TLabel',
                        font=self.fonts['verification_code'],
                        foreground=self.colors['primary'])
    
    def _configure_scrollbar_style(self, style):
        """配置滚动条样式"""
        style.configure('TScrollbar',
                        background=self.colors['bg_light'],
                        troughcolor=self.colors['bg_light'],
                        borderwidth=0,
                        arrowsize=13)
        
        style.map('TScrollbar',
                 background=[('active', self.colors['primary_light']),
                             ('disabled', self.colors['bg_light'])],
                 troughcolor=[('active', self.colors['bg_light']),
                              ('disabled', self.colors['bg_light'])])

    def _configure_combobox_style(self, style):
        """配置下拉框样式"""
        style.configure('TCombobox',
                        font=self.fonts['body'],
                        padding=self.styles['padding'],  # 使用更大的内边距
                        fieldbackground=self.colors['white'],
                        borderwidth=1,
                        relief='solid')

        style.map('TCombobox',
                 bordercolor=[('focus', self.colors['primary']),
                              ('disabled', self.colors['disabled'])],
                 fieldbackground=[('disabled', self.colors['disabled'])])
    
    def get_font(self, font_key):
        """获取指定的字体"""
        if font_key in self.fonts:
            return self.fonts[font_key]
        return self.fonts['body']  # 默认返回正文字体
    
    def get_color(self, color_key):
        """获取指定的颜色"""
        if color_key in self.colors:
            return self.colors[color_key]
        return self.colors['text_primary']  # 默认返回主要文本颜色


# 创建全局主题管理器实例
theme_manager = ThemeManager()