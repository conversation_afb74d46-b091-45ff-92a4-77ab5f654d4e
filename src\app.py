"""
应用程序主类
"""
import sys
import threading
import traceback
from tkinter import messagebox
from src.gui.simple_main_window import Simple<PERSON>ainWindow
from src.config_manager import ConfigManager
from src.email_generator import EmailGenerator
from src.tempmail_service import TempMailService
from src.log_manager import LogManager
from src.workflow_manager import WorkflowManager
from src.error_handler import ErrorHandler
from src.models import AppConfig
from src.exceptions import (
    ConfigurationError, TempMailAPIError, NetworkError, 
    VerificationCodeError, ValidationError, FileOperationError,
    WorkflowError, UIError
)


class Application:
    """应用程序主类"""
    
    def __init__(self):
        """初始化应用程序"""
        self.config_manager = ConfigManager()
        self.log_manager = LogManager()
        self.email_generator = None
        self.tempmail_service = None
        self.app_config = None
        self.main_window = None
        self.workflow_manager = None
        
        # 创建错误处理器
        self.error_handler = ErrorHandler(self.log_manager)
        
        # 设置全局异常处理
        sys.excepthook = self.error_handler.handle_exception
    
    # 旧的异常处理方法已被ErrorHandler类替代
    
    def _load_config(self):
        """加载配置"""
        try:
            self.app_config = self.config_manager.load_config()
            if self.app_config is None:
                # 首次启动，使用默认配置
                self.app_config = self.config_manager.get_default_config()
                self.log_manager.add_log("首次启动，使用默认配置", "INFO")
                # 保存默认配置
                try:
                    self.config_manager.save_config(self.app_config)
                    self.log_manager.add_log("已保存默认配置", "INFO")
                except Exception as save_error:
                    self.log_manager.add_log(f"无法保存默认配置: {str(save_error)}", "WARNING")
            else:
                self.log_manager.add_log("配置加载成功", "SUCCESS")

            # 无论是否首次启动，都初始化服务
            self._initialize_services()
        except ConfigurationError as e:
            # 使用错误处理器处理异常
            exc_type, exc_value, exc_traceback = sys.exc_info()
            self.error_handler.handle_exception(exc_type, exc_value, exc_traceback)
            
            # 使用默认配置
            self.app_config = self.config_manager.get_default_config()
            self.log_manager.add_log(f"配置错误: {str(e)}，已恢复为默认配置", "WARNING")
            
            # 尝试保存默认配置
            try:
                self.config_manager.save_config(self.app_config)
                self.log_manager.add_log("已保存默认配置", "INFO")
            except Exception as save_error:
                self.log_manager.add_log(f"无法保存默认配置: {str(save_error)}", "ERROR")
        except Exception as e:
            # 使用错误处理器处理异常
            exc_type, exc_value, exc_traceback = sys.exc_info()
            self.error_handler.handle_exception(exc_type, exc_value, exc_traceback)
            
            # 使用默认配置
            self.app_config = self.config_manager.get_default_config()
            self.log_manager.add_log(f"发生未知错误: {str(e)}，已恢复为默认配置", "ERROR")
    
    def _initialize_services(self):
        """初始化服务"""
        try:
            # 初始化邮箱生成器
            self.email_generator = EmailGenerator(self.app_config.email_domain)
            
            # 初始化临时邮箱服务
            self.tempmail_service = TempMailService(
                self.app_config.temp_mail,
                self.app_config.max_retries,
                self.app_config.retry_interval
            )
            
            self.log_manager.add_log("服务初始化完成", "SUCCESS")
            
        except Exception as e:
            # 使用错误处理器处理异常
            exc_type, exc_value, exc_traceback = sys.exc_info()
            self.error_handler.handle_exception(exc_type, exc_value, exc_traceback)
            
            # 记录错误日志
            self.log_manager.add_log(f"服务初始化失败: {str(e)}", "ERROR")
    
    def save_config(self, config: AppConfig) -> bool:
        """保存配置"""
        try:
            # 验证配置
            self.config_manager.validate_config(config)
            
            # 保存配置
            success = self.config_manager.save_config(config)
            if success:
                self.app_config = config
                self._initialize_services()
                self.log_manager.add_log("配置保存成功", "SUCCESS")
                return True
            else:
                raise ConfigurationError("配置保存失败")
                
        except Exception as e:
            # 使用错误处理器处理异常
            exc_type, exc_value, exc_traceback = sys.exc_info()
            self.error_handler.handle_exception(exc_type, exc_value, exc_traceback)
            return False
    
    def generate_email(self, count: int = 1):
        """生成邮箱"""
        def _generate():
            try:
                if self.main_window:
                    self.main_window.status_bar.set_status("正在生成邮箱...", "INFO")

                if not self.email_generator:
                    raise ValidationError("邮箱生成器未初始化，请先配置应用")

                if count < 1 or count > 100:
                    raise ValidationError("生成数量必须在1-100之间")

                if count == 1:
                    email_info = self.email_generator.generate_single_email()
                    emails = [email_info]
                else:
                    emails = self.email_generator.generate_batch_emails(count)

                # 更新邮箱面板
                if self.main_window:
                    self.main_window.email_panel.add_emails(emails)

                self.log_manager.add_log(f"成功生成{len(emails)}个邮箱", "SUCCESS")

                if self.main_window:
                    self.main_window.status_bar.set_status("邮箱生成完成", "SUCCESS")

                return emails
            except Exception as e:
                # 使用错误处理器处理异常
                exc_type, exc_value, exc_traceback = sys.exc_info()
                self.error_handler.handle_exception(exc_type, exc_value, exc_traceback)
                
                # 更新状态栏
                if self.main_window:
                    self.main_window.status_bar.set_status("邮箱生成失败", "ERROR")
                
                return None
        
        # 在后台线程中执行
        return threading.Thread(target=_generate, daemon=True).start()
    
    def get_verification_code(self):
        """获取验证码"""
        def _get_code():
            try:
                if self.main_window:
                    self.main_window.status_bar.set_status("正在获取验证码...", "INFO")
                
                if not self.tempmail_service:
                    raise ValidationError("临时邮箱服务未初始化，请先配置应用")
                
                # 测试连接
                if not self.tempmail_service.test_connection():
                    raise NetworkError("无法连接到临时邮箱服务，请检查配置和网络连接")
                
                code = self.tempmail_service.get_verification_code_with_retry()
                
                # 更新验证码面板
                if self.main_window:
                    self.main_window.verification_panel.set_verification_code(code)
                
                self.log_manager.add_log(f"成功获取验证码: {code}", "SUCCESS")
                
                if self.main_window:
                    self.main_window.status_bar.set_status("验证码获取完成", "SUCCESS")
                
                return code
                
            except Exception as e:
                # 使用错误处理器处理异常
                exc_type, exc_value, exc_traceback = sys.exc_info()
                self.error_handler.handle_exception(exc_type, exc_value, exc_traceback)
                
                # 更新状态栏
                if self.main_window:
                    self.main_window.status_bar.set_status("验证码获取失败", "ERROR")
                
                return None
        
        # 在后台线程中执行
        return threading.Thread(target=_get_code, daemon=True).start()
    
    def run(self):
        """运行应用程序"""
        try:
            # 加载配置
            self._load_config()
            
            # 创建简化版主窗口
            self.main_window = SimpleMainWindow(self)
            
            # 初始化工作流程管理器
            self.workflow_manager = WorkflowManager(self)
            
            # 记录启动日志
            self.log_manager.add_log("应用程序启动", "INFO")
            
            # 运行主循环
            self.main_window.run()
            
        except Exception as e:
            # 使用错误处理器处理异常
            exc_type, exc_value, exc_traceback = sys.exc_info()
            self.error_handler.handle_exception(exc_type, exc_value, exc_traceback)
            
            # 严重错误，退出应用程序
            sys.exit(1)