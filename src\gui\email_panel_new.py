"""
邮箱生成面板 (美化版)
"""
import tkinter as tk
from tkinter import ttk, messagebox
import pyperclip
from typing import List
from src.models import EmailInfo
from src.gui.theme_manager import theme_manager
from src.gui.custom_widgets import ModernButton, StyledTreeview
from src.gui.layout_manager import LayoutManager


class EmailPanel:
    """邮箱生成面板"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.emails = []
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面组件"""
        self.frame = ttk.Frame(self.parent)
        self.frame.configure(style='TFrame')
        
        # 创建标题区域
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = ttk.Label(title_frame, text="邮箱生成", 
                               style='Title.TLabel')
        title_label.pack(anchor='center')
        
        # 创建控制面板
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill=tk.X, padx=20, pady=(10, 20))
        
        # 创建左侧控制区域
        left_control = ttk.Frame(control_frame)
        left_control.pack(side=tk.LEFT)
        
        # 生成数量选择
        # count_frame = ttk.Frame(left_control)
        # count_frame.pack(side=tk.LEFT, padx=(0, 15))
        
        # ttk.Label(count_frame, text="生成数量:").pack(side=tk.LEFT)
        
        # self.count_var = tk.StringVar(value="1")
        # count_combo = ttk.Combobox(count_frame, textvariable=self.count_var, 
        #                           values=["1", "5", "10", "20", "50"], 
        #                           width=10, state="readonly")
        # count_combo.pack(side=tk.LEFT, padx=5)
        
        # 创建按钮组
        generate_btn = ModernButton(control_frame, text="生成邮箱", 
                                  command=self._on_generate_click)
        
        clear_btn = ModernButton(control_frame, text="清除列表", 
                               style='Secondary.TButton',
                               command=self._on_clear_click)
        
        # batch_code_btn = ModernButton(control_frame, text="批量获取验证码", 
        #                             style='Success.TButton',
        #                             command=self._on_batch_code_click)
        
        # 使用布局管理器创建按钮组
        buttons = [generate_btn, clear_btn, batch_code_btn]
        button_group = LayoutManager.create_button_group(control_frame, buttons)
        button_group.pack(side=tk.RIGHT)
        
        # 创建邮箱列表区域
        list_frame = LayoutManager.create_section_frame(self.frame, "生成的邮箱地址")
        
        # 创建树形视图
        columns = ("序号", "邮箱地址", "生成时间")
        self.tree = StyledTreeview(list_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        self.tree.heading("序号", text="序号")
        self.tree.heading("邮箱地址", text="邮箱地址")
        self.tree.heading("生成时间", text="生成时间")
        self.tree.column("序号", width=60, anchor=tk.CENTER)
        self.tree.column("邮箱地址", width=400, anchor=tk.W)
        self.tree.column("生成时间", width=150, anchor=tk.CENTER)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)
        
        # 绑定双击事件
        self.tree.bind("<Double-1>", self._on_item_double_click)
        
        # 右键菜单
        self.context_menu = tk.Menu(self.tree, tearoff=0)
        self.context_menu.add_command(label="复制邮箱", command=self._copy_selected_email)
        self.context_menu.add_command(label="复制所有邮箱", command=self._copy_all_emails)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除选中", command=self._delete_selected)
        
        self.tree.bind("<Button-3>", self._show_context_menu)
        
        # 底部信息区域
        info_frame = ttk.Frame(self.frame)
        info_frame.pack(fill=tk.X, padx=20, pady=(5, 15))
        
        self.info_label = ttk.Label(info_frame, text="提示: 双击邮箱地址可复制到剪贴板",
                                  style='Small.TLabel')
        self.info_label.pack(side=tk.LEFT)
        
        self.count_label = ttk.Label(info_frame, text="总计: 0 个邮箱")
        self.count_label.pack(side=tk.RIGHT)
    
    def _on_generate_click(self):
        """生成按钮点击事件"""
        try:
            count = int(self.count_var.get())
            if count < 1 or count > 100:
                messagebox.showerror("错误", "生成数量必须在1-100之间")
                return
            
            self.main_window.generate_email(count)
            
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数量")
    
    def _on_batch_code_click(self):
        """批量获取验证码按钮点击事件"""
        if not self.main_window.app.workflow_manager:
            messagebox.showerror("错误", "工作流程管理器未初始化，请重启应用")
            return
            
        # 创建对话框获取参数
        dialog = tk.Toplevel(self.main_window.root)
        dialog.title("批量获取验证码")
        dialog.geometry("300x200")
        dialog.resizable(False, False)
        dialog.transient(self.main_window.root)  # 设置为主窗口的子窗口
        dialog.grab_set()  # 模态对话框
        
        # 应用主题
        dialog.configure(bg=theme_manager.colors['bg_light'])
        
        # 居中显示
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")
        
        # 创建表单
        title_label = ttk.Label(dialog, text="批量获取验证码设置", style='Subtitle.TLabel')
        title_label.pack(pady=(15, 20))
        
        form_frame = ttk.Frame(dialog)
        form_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        # 数量
        count_frame, count_spinbox = LayoutManager.create_form_field(
            form_frame, "获取数量:", ttk.Spinbox, 
            {"from_": 1, "to": 20, "width": 10}
        )
        count_frame.pack(fill=tk.X, pady=5)
        count_var = tk.StringVar(value="5")
        count_spinbox.config(textvariable=count_var)
        
        # 间隔
        interval_frame, interval_spinbox = LayoutManager.create_form_field(
            form_frame, "间隔时间(秒):", ttk.Spinbox, 
            {"from_": 5, "to": 60, "width": 10}
        )
        interval_frame.pack(fill=tk.X, pady=5)
        interval_var = tk.StringVar(value="10")
        interval_spinbox.config(textvariable=interval_var)
        
        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=(10, 15))
        
        def on_cancel():
            dialog.destroy()
        
        def on_start():
            try:
                count = int(count_var.get())
                interval = int(interval_var.get())
                
                if count < 1 or count > 20:
                    messagebox.showerror("错误", "获取数量必须在1-20之间")
                    return
                
                if interval < 5 or interval > 60:
                    messagebox.showerror("错误", "间隔时间必须在5-60秒之间")
                    return
                
                # 关闭对话框
                dialog.destroy()
                
                # 启动批量验证码获取工作流程
                self.main_window.app.workflow_manager.run_batch_verification_workflow(count, interval)
                
                # 切换到验证码获取面板
                self.main_window.notebook.select(1)  # 选择验证码获取标签页
                
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")
        
        cancel_btn = ModernButton(button_frame, text="取消", 
                                style='Secondary.TButton',
                                command=on_cancel)
        start_btn = ModernButton(button_frame, text="开始获取", 
                               command=on_start)
        
        cancel_btn.pack(side=tk.RIGHT, padx=(5, 0))
        start_btn.pack(side=tk.RIGHT)
    
    def _on_clear_click(self):
        """清除按钮点击事件"""
        if self.emails:
            result = messagebox.askyesno("确认", "确定要清除所有邮箱吗？")
            if result:
                self.clear_emails()
    
    def _on_item_double_click(self, event):
        """列表项双击事件"""
        self._copy_selected_email()
    
    def _show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.post(event.x_root, event.y_root)
        except:
            pass
    
    def _copy_selected_email(self):
        """复制选中的邮箱"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一个邮箱")
            return
        
        item = self.tree.item(selection[0])
        email = item['values'][1]  # 邮箱地址在第二列
        
        try:
            pyperclip.copy(email)
            self.main_window.app.log_manager.add_log(f"已复制邮箱: {email}", "SUCCESS")
            
            # 显示复制成功的视觉反馈
            self.main_window.status_bar.set_status(f"邮箱 {email} 已复制到剪贴板", "SUCCESS")
        except Exception as e:
            messagebox.showerror("错误", f"复制失败: {str(e)}")
    
    def _copy_all_emails(self):
        """复制所有邮箱"""
        if not self.emails:
            messagebox.showwarning("提示", "没有邮箱可复制")
            return
        
        email_list = [email.address for email in self.emails]
        email_text = "\n".join(email_list)
        
        try:
            pyperclip.copy(email_text)
            self.main_window.app.log_manager.add_log(f"已复制{len(email_list)}个邮箱", "SUCCESS")
            
            # 显示复制成功的视觉反馈
            self.main_window.status_bar.set_status(f"已复制{len(email_list)}个邮箱到剪贴板", "SUCCESS")
        except Exception as e:
            messagebox.showerror("错误", f"复制失败: {str(e)}")
    
    def _delete_selected(self):
        """删除选中的邮箱"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要删除的邮箱")
            return
        
        result = messagebox.askyesno("确认", "确定要删除选中的邮箱吗？")
        if result:
            # 获取选中项的索引
            for item_id in selection:
                item = self.tree.item(item_id)
                index = int(item['values'][0]) - 1  # 序号从1开始，索引从0开始
                
                # 从列表中删除
                if 0 <= index < len(self.emails):
                    del self.emails[index]
            
            # 刷新显示
            self._refresh_tree()
    
    def add_emails(self, emails: List[EmailInfo]):
        """添加邮箱到列表"""
        self.emails.extend(emails)
        self._refresh_tree()
    
    def clear_emails(self):
        """清除所有邮箱"""
        self.emails.clear()
        self._refresh_tree()
        self.main_window.app.log_manager.add_log("已清除所有邮箱", "INFO")
    
    def _refresh_tree(self):
        """刷新树形视图"""
        # 清除现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加新项目
        for i, email in enumerate(self.emails, 1):
            time_str = email.generated_time.strftime("%Y-%m-%d %H:%M:%S")
            tag = 'even' if i % 2 == 0 else 'odd'
            self.tree.insert("", tk.END, values=(i, email.address, time_str), tags=(tag,))
        
        # 更新计数标签
        self.count_label.config(text=f"总计: {len(self.emails)} 个邮箱")