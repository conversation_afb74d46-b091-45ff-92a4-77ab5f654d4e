'''
Author: ya<PERSON><PERSON> <EMAIL>
Date: 2025-07-16 21:59:39
LastEditors: yangyang <EMAIL>
LastEditTime: 2025-07-16 22:20:13
FilePath: \Augment\src\email_generator.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
"""
邮箱生成器
"""
import random
import time
from typing import List, Tuple
from datetime import datetime
from src.models import EmailInfo


class EmailGenerator:
    """邮箱生成器"""
    
    FIRST_NAMES = ["linda", "john", "mary", "david", "sarah", "michael", "jennifer", 
                   "robert", "lisa", "william", "karen", "james", "nancy", "charles"]
    
    LAST_NAMES = ["garcia", "smith", "johnson", "brown", "davis", "miller", "wilson",
                  "moore", "taylor", "anderson", "thomas", "jackson", "white", "harris"]
    
    def __init__(self, email_domain: str = "@465447.xyz"):
        self.email_domain = email_domain
    
    def get_random_name_parts(self) -> Tuple[str, str]:
        """获取随机的名字部分"""
        first_name = random.choice(self.FIRST_NAMES)
        last_name = random.choice(self.LAST_NAMES)
        return first_name, last_name
    
    def generate_single_email(self) -> EmailInfo:
        """生成单个邮箱地址"""
        first_name, last_name = self.get_random_name_parts()
        
        # 生成时间戳（36进制）
        timestamp = int(time.time() * 1000)  # 毫秒级时间戳
        timestamp_36 = self._to_base36(timestamp)
        
        # 生成4位随机数
        random_num = random.randint(1000, 9999)
        
        # 组合用户名
        username = f"{first_name}{last_name}{timestamp_36}{random_num}"
        email_address = f"{username}{self.email_domain}"
        
        return EmailInfo(
            address=email_address,
            generated_time=datetime.now()
        )
    
    def generate_batch_emails(self, count: int) -> List[EmailInfo]:
        """批量生成邮箱地址"""
        if count < 1 or count > 100:
            raise ValueError("生成数量必须在1-100之间")
        
        emails = []
        for _ in range(count):
            # 添加小延迟确保时间戳不同
            time.sleep(0.001)
            emails.append(self.generate_single_email())
        
        return emails
    
    def _to_base36(self, num: int) -> str:
        """将数字转换为36进制字符串"""
        if num == 0:
            return "0"
        
        digits = "0123456789abcdefghijklmnopqrstuvwxyz"
        result = ""
        
        while num > 0:
            result = digits[num % 36] + result
            num //= 36
        
        return result
    
    def validate_email_format(self, email: str) -> bool:
        """验证邮箱格式"""
        if not email:
            return False
        
        if '@' not in email:
            return False
        
        parts = email.split('@')
        if len(parts) != 2:
            return False
        
        username, domain = parts
        if not username or not domain:
            return False
        
        # 检查用户名是否包含有效字符
        if not username.replace('_', '').replace('-', '').replace('.', '').isalnum():
            return False
        
        return True