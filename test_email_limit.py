#!/usr/bin/env python3
"""
测试体验用户邮箱生成限制
验证体验用户只能生成1个邮箱的权限控制
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from src.user_management.user_permission_system import UserPermissionManager

def test_trial_user_email_limit():
    """测试体验用户邮箱生成限制"""
    print("=" * 60)
    print("测试体验用户邮箱生成限制")
    print("=" * 60)
    
    # 初始化权限管理器
    permission_manager = UserPermissionManager()
    
    # 登录体验用户
    print("1. 登录体验用户...")
    user_info = permission_manager.login_user("体验123")
    if not user_info:
        print("❌ 登录失败：用户不存在")
        return
    
    print(f"✅ 登录成功: {user_info.username} ({user_info.role.value})")
    print(f"   当前邮箱生成次数: {user_info.email_count}")
    print(f"   用户备注: {user_info.notes}")
    
    # 检查邮箱生成权限
    print("\n2. 检查邮箱生成权限...")
    allowed, reason, used, limit = permission_manager.check_email_limit()
    
    print(f"   是否允许生成: {'✅ 是' if allowed else '❌ 否'}")
    print(f"   限制数量: {'无限制' if limit == -1 else f'{limit}次/日'}")
    print(f"   已使用次数: {used}次")
    print(f"   状态说明: {reason}")
    
    # 测试权限控制
    print("\n3. 测试权限控制...")
    if allowed:
        print("⚠️  权限检查显示允许生成，但体验用户应该已达到限制！")
        print("   这表明权限控制可能存在问题。")
    else:
        print("✅ 权限控制正常：体验用户已达到邮箱生成限制")
        print("   系统正确阻止了超出限制的邮箱生成请求。")
    
    # 显示权限配置
    print("\n4. 权限配置详情...")
    permission = permission_manager.permissions.get(user_info.role)
    if permission:
        print(f"   角色: {permission.role.value}")
        print(f"   邮箱限制: {'无限制' if permission.email_limit == -1 else f'{permission.email_limit}次/日'}")
        print(f"   验证码限制: {'无限制' if permission.verification_limit == -1 else f'{permission.verification_limit}次/日'}")
        print(f"   频率限制: {permission.rate_limit_seconds}秒")
        print(f"   可用功能: {', '.join(permission.features)}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_trial_user_email_limit()
