@echo off
chcp 65001 >nul
echo ========================================
echo AugmentCode自动注册助手 - 打包脚本
echo ========================================
echo.

echo [1/5] 清理之前的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del /q "*.spec"
echo 清理完成！
echo.

echo [2/5] 检查依赖项...
python -c "import tkinter, requests, pyperclip; print('所有依赖项检查通过！')"
if errorlevel 1 (
    echo 错误：缺少必要的依赖项！
    echo 请运行：pip install requests pyperclip
    pause
    exit /b 1
)
echo.

echo [3/5] 开始打包...
pyinstaller --clean ^
    --onefile ^
    --windowed ^
    --name "AugmentCode自动注册助手" ^
    --add-data "data;data" ^
    --add-data "docs;docs" ^
    --hidden-import "tkinter" ^
    --hidden-import "tkinter.ttk" ^
    --hidden-import "tkinter.messagebox" ^
    --hidden-import "requests" ^
    --hidden-import "pyperclip" ^
    --hidden-import "src.app" ^
    --hidden-import "src.gui.simple_main_window" ^
    --hidden-import "src.user_management.user_permission_system" ^
    --hidden-import "src.user_management.email_storage_manager" ^
    --exclude-module "matplotlib" ^
    --exclude-module "numpy" ^
    --exclude-module "pandas" ^
    --version-file "version_info.txt" ^
    run_simple_app.py

if errorlevel 1 (
    echo 错误：打包失败！
    pause
    exit /b 1
)
echo.

echo [4/5] 检查打包结果...
if exist "dist\AugmentCode自动注册助手.exe" (
    echo 打包成功！可执行文件位置：dist\AugmentCode自动注册助手.exe
) else (
    echo 错误：未找到生成的可执行文件！
    pause
    exit /b 1
)
echo.

echo [5/5] 创建发布目录...
if not exist "release" mkdir "release"
copy "dist\AugmentCode自动注册助手.exe" "release\"
copy "README.md" "release\" 2>nul
copy "docs\简化版使用指导.md" "release\使用说明.md" 2>nul
copy "docs\快速参考.md" "release\" 2>nul

echo.
echo ========================================
echo 打包完成！
echo ========================================
echo 可执行文件位置：release\AugmentCode自动注册助手.exe
echo 文件大小：
for %%I in ("release\AugmentCode自动注册助手.exe") do echo %%~zI 字节
echo.
echo 发布文件夹包含：
dir /b "release"
echo.
echo 现在可以将 release 文件夹分发给用户！
echo ========================================
pause
