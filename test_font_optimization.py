#!/usr/bin/env python3
"""
字体优化测试工具
用于测试和比较字体优化前后的效果
"""
import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from src.gui.theme_manager import theme_manager


class FontTestWindow:
    """字体测试窗口"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("字体优化测试")
        self.root.geometry("800x600")
        
        # 应用主题
        self.style = theme_manager.apply_theme(self.root)
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建测试界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="字体优化测试", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # 系统信息
        info_frame = ttk.LabelFrame(main_frame, text="系统信息")
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        system_info = f"操作系统: {theme_manager.system}\n"
        system_info += f"DPI缩放: {theme_manager.dpi_scale:.2f}\n"
        system_info += f"系统字体: {theme_manager.system_font}\n"
        system_info += f"等宽字体: {theme_manager.monospace_font}"
        
        info_label = ttk.Label(info_frame, text=system_info, justify=tk.LEFT)
        info_label.pack(padx=15, pady=10, anchor='w')
        
        # 字体样式测试
        font_test_frame = ttk.LabelFrame(main_frame, text="字体样式测试")
        font_test_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # 创建滚动框架
        canvas = tk.Canvas(font_test_frame)
        scrollbar = ttk.Scrollbar(font_test_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 测试各种字体样式
        font_tests = [
            ("标题字体", "Title.TLabel", "这是标题字体测试 - Title Font Test"),
            ("副标题字体", "Subtitle.TLabel", "这是副标题字体测试 - Subtitle Font Test"),
            ("正文字体", "TLabel", "这是正文字体测试 - Body Font Test\n支持中英文混合显示"),
            ("小字体", "Small.TLabel", "这是小字体测试 - Small Font Test"),
            ("验证码字体", "VerificationCode.TLabel", "123456"),
            ("代码字体", "Code.TLabel", "print('Hello, World!')"),
        ]
        
        for i, (name, style, text) in enumerate(font_tests):
            # 创建测试行
            test_frame = ttk.Frame(scrollable_frame)
            test_frame.pack(fill=tk.X, padx=10, pady=5)
            
            # 字体名称
            name_label = ttk.Label(test_frame, text=f"{name}:", width=15, anchor='w')
            name_label.pack(side=tk.LEFT, padx=(0, 10))
            
            # 字体示例
            sample_label = ttk.Label(test_frame, text=text, style=style)
            sample_label.pack(side=tk.LEFT, anchor='w')
        
        # 布局滚动框架
        canvas.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side="right", fill="y", padx=(0, 10), pady=10)
        
        # 控件测试
        widget_test_frame = ttk.LabelFrame(main_frame, text="控件字体测试")
        widget_test_frame.pack(fill=tk.X)
        
        # 按钮测试
        button_frame = ttk.Frame(widget_test_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(button_frame, text="按钮:", width=15, anchor='w').pack(side=tk.LEFT)
        ttk.Button(button_frame, text="测试按钮").pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Secondary", style='Secondary.TButton').pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Success", style='Success.TButton').pack(side=tk.LEFT, padx=5)
        
        # 输入框测试
        entry_frame = ttk.Frame(widget_test_frame)
        entry_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(entry_frame, text="输入框:", width=15, anchor='w').pack(side=tk.LEFT)
        entry = ttk.Entry(entry_frame, width=30)
        entry.pack(side=tk.LEFT, padx=5)
        entry.insert(0, "测试输入框字体")
        
        # 下拉框测试
        combo_frame = ttk.Frame(widget_test_frame)
        combo_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(combo_frame, text="下拉框:", width=15, anchor='w').pack(side=tk.LEFT)
        combo = ttk.Combobox(combo_frame, values=["选项1", "选项2", "选项3"], width=27)
        combo.pack(side=tk.LEFT, padx=5)
        combo.set("测试下拉框字体")
        
        # 表格测试
        tree_frame = ttk.Frame(widget_test_frame)
        tree_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(tree_frame, text="表格:", width=15, anchor='w').pack(side=tk.LEFT)
        
        tree = ttk.Treeview(tree_frame, columns=("col1", "col2"), show="headings", height=3)
        tree.heading("col1", text="列1")
        tree.heading("col2", text="列2")
        tree.column("col1", width=100)
        tree.column("col2", width=150)
        
        # 添加测试数据
        tree.insert("", "end", values=("测试数据1", "Test Data 1"))
        tree.insert("", "end", values=("测试数据2", "Test Data 2"))
        tree.insert("", "end", values=("中英混合", "Mixed Text"))
        
        tree.pack(side=tk.LEFT, padx=5)
        
        # 底部信息
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=(10, 0))
        
        info_text = "字体优化说明: 已启用高DPI支持，自动检测最佳字体，优化字体大小和渲染效果"
        ttk.Label(bottom_frame, text=info_text, style='Small.TLabel', 
                 wraplength=700, justify=tk.CENTER).pack()
    
    def run(self):
        """运行测试窗口"""
        self.root.mainloop()


def main():
    """主函数"""
    print("启动字体优化测试...")
    print("=" * 50)
    print("测试内容:")
    print("• 系统字体检测")
    print("• DPI缩放支持")
    print("• 各种字体样式")
    print("• 控件字体渲染")
    print("=" * 50)
    
    try:
        test_window = FontTestWindow()
        test_window.run()
    except Exception as e:
        print(f"测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
