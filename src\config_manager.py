"""
配置管理器
"""
import os
import configparser
import base64
from pathlib import Path
from typing import Dict, Any, Optional
from src.models import TempMailConfig, AppConfig
from src.exceptions import ConfigurationError


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_dir = self._get_config_dir()
        self.config_file = self.config_dir / "config.ini"
        self._ensure_config_dir()
    
    def _get_config_dir(self) -> Path:
        """获取配置目录"""
        if os.name == 'nt':  # Windows
            config_dir = Path(os.environ.get('APPDATA', '')) / "AugmentAutoGUI"
        else:  # macOS/Linux
            config_dir = Path.home() / ".augment_auto_gui"
        return config_dir
    
    def _ensure_config_dir(self):
        """确保配置目录存在"""
        try:
            self.config_dir.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            raise ConfigurationError(f"无法创建配置目录: {str(e)}")
    
    def _encode_sensitive_data(self, data: str) -> str:
        """编码敏感数据"""
        return base64.b64encode(data.encode()).decode()
    
    def _decode_sensitive_data(self, data: str) -> str:
        """解码敏感数据"""
        try:
            return base64.b64decode(data.encode()).decode()
        except Exception:
            return data  # 如果解码失败，返回原始数据
    
    def load_config(self) -> Optional[AppConfig]:
        """加载配置"""
        if not self.config_file.exists():
            return None
        
        try:
            config = configparser.ConfigParser()
            config.read(self.config_file, encoding='utf-8')
            
            # 检查并读取临时邮箱配置
            if not config.has_section('TempMail'):
                raise ConfigurationError("配置文件缺少 'TempMail' 节")
                
            temp_mail_config = TempMailConfig(
                username=config['TempMail'].get('username', ''),
                email_extension=config['TempMail'].get('email_extension', ''),
                epin=self._decode_sensitive_data(config['TempMail'].get('epin', ''))
            )
            
            # 读取应用配置
            email_domain = '@465447.xyz'  # 默认值
            max_retries = 5  # 默认值
            retry_interval = 3000  # 默认值
            
            # 如果存在App节，则读取其中的选项
            if config.has_section('App'):
                if config.has_option('App', 'email_domain'):
                    email_domain = config['App'].get('email_domain')
                if config.has_option('App', 'max_retries'):
                    max_retries = config['App'].getint('max_retries')
                if config.has_option('App', 'retry_interval'):
                    retry_interval = config['App'].getint('retry_interval')
            
            app_config = AppConfig(
                temp_mail=temp_mail_config,
                email_domain=email_domain,
                max_retries=max_retries,
                retry_interval=retry_interval
            )
            
            return app_config
            
        except ConfigurationError as e:
            # 直接重新抛出配置错误
            raise
        except Exception as e:
            # 其他异常包装为配置错误
            raise ConfigurationError(f"加载配置失败: {e}")
    
    def save_config(self, config: AppConfig) -> bool:
        """保存配置"""
        try:
            # 首先验证配置
            self.validate_config(config)
            
            config_parser = configparser.ConfigParser()
            
            # 临时邮箱配置
            config_parser['TempMail'] = {
                'username': config.temp_mail.username,
                'email_extension': config.temp_mail.email_extension,
                'epin': self._encode_sensitive_data(config.temp_mail.epin)
            }
            
            # 应用配置
            config_parser['App'] = {
                'email_domain': config.email_domain,
                'max_retries': str(config.max_retries),
                'retry_interval': str(config.retry_interval)
            }
            
            # 确保配置目录存在
            self._ensure_config_dir()
            
            # 保存配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                config_parser.write(f)
            
            # 记录配置保存成功的日志（如果有日志管理器）
            # 由于ConfigManager不直接依赖LogManager，所以在Application中处理日志记录
            
            return True
            
        except ConfigurationError as e:
            # 直接重新抛出配置错误
            raise
        except Exception as e:
            # 其他异常包装为配置错误
            raise ConfigurationError(f"保存配置失败: {e}")
    
    def validate_config(self, config: AppConfig) -> bool:
        """验证配置"""
        # 验证临时邮箱配置
        if not config.temp_mail:
            raise ConfigurationError("临时邮箱配置不能为空")
            
        if not config.temp_mail.username:
            raise ConfigurationError("临时邮箱用户名不能为空")
        
        if not config.temp_mail.email_extension:
            raise ConfigurationError("临时邮箱扩展名不能为空")
        
        if not config.temp_mail.email_extension.startswith('@'):
            raise ConfigurationError("临时邮箱扩展名必须以@开头，例如：@fexpost.com")
        
        # 验证应用配置
        if not config.email_domain:
            raise ConfigurationError("邮箱域名不能为空")
            
        if not config.email_domain.startswith('@'):
            raise ConfigurationError("邮箱域名必须以@开头，例如：@465447.xyz")
        
        if not isinstance(config.max_retries, int):
            raise ConfigurationError("重试次数必须是整数")
            
        if config.max_retries < 1 or config.max_retries > 10:
            raise ConfigurationError("重试次数必须在1-10之间，当前值：" + str(config.max_retries))
        
        if not isinstance(config.retry_interval, int):
            raise ConfigurationError("重试间隔必须是整数")
            
        if config.retry_interval < 1000 or config.retry_interval > 10000:
            raise ConfigurationError("重试间隔必须在1000-10000毫秒之间，当前值：" + str(config.retry_interval))
        
        return True
    
    def get_default_config(self) -> AppConfig:
        """获取默认配置"""
        return AppConfig(
            temp_mail=TempMailConfig(
                username="etgapu",  # 根据图片设置默认用户名
                email_extension="@fexpost.com",  # 根据图片设置默认邮箱扩展名
                epin=""  # epin留空，根据需要可以设置
            ),
            email_domain="@465447.xyz",  # 根据图片设置默认邮箱域名
            max_retries=5,
            retry_interval=3000
        )