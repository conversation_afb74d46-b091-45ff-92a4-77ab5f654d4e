#!/usr/bin/env python3
"""
Markdown转PDF工具
使用markdown2和weasyprint库
"""

import os
import sys
from pathlib import Path

def install_dependencies():
    """安装必要的依赖"""
    try:
        import markdown2
        import weasyprint
        print("✅ 依赖已安装")
        return True
    except ImportError:
        print("📦 正在安装依赖...")
        os.system("pip install markdown2 weasyprint")
        try:
            import markdown2
            import weasyprint
            print("✅ 依赖安装成功")
            return True
        except ImportError:
            print("❌ 依赖安装失败")
            return False

def convert_md_to_pdf(md_file, output_file=None):
    """将Markdown文件转换为PDF"""
    try:
        import markdown2
        import weasyprint
        
        # 读取Markdown文件
        with open(md_file, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # 转换为HTML
        html_content = markdown2.markdown(md_content, extras=['fenced-code-blocks', 'tables'])
        
        # 添加CSS样式
        css_style = """
        <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        h1 { font-size: 28px; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        h2 { font-size: 24px; border-bottom: 1px solid #bdc3c7; padding-bottom: 8px; }
        h3 { font-size: 20px; }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', monospace;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border-left: 4px solid #3498db;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        blockquote {
            border-left: 4px solid #3498db;
            margin: 20px 0;
            padding-left: 20px;
            color: #666;
        }
        ul, ol {
            margin: 15px 0;
            padding-left: 30px;
        }
        li {
            margin: 5px 0;
        }
        </style>
        """
        
        # 完整的HTML文档
        full_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Markdown转PDF</title>
            {css_style}
        </head>
        <body>
            {html_content}
        </body>
        </html>
        """
        
        # 确定输出文件名
        if output_file is None:
            output_file = Path(md_file).with_suffix('.pdf')
        
        # 转换为PDF
        weasyprint.HTML(string=full_html).write_pdf(output_file)
        
        print(f"✅ 转换成功: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False

def main():
    """主函数"""
    print("🔄 Markdown转PDF工具")
    print("=" * 40)
    
    # 检查并安装依赖
    if not install_dependencies():
        return
    
    # 获取输入文件
    if len(sys.argv) > 1:
        md_file = sys.argv[1]
    else:
        md_file = input("请输入Markdown文件路径: ").strip()
    
    # 检查文件是否存在
    if not os.path.exists(md_file):
        print(f"❌ 文件不存在: {md_file}")
        return
    
    # 获取输出文件（可选）
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    else:
        output_file = None
    
    # 执行转换
    convert_md_to_pdf(md_file, output_file)

if __name__ == "__main__":
    main()
