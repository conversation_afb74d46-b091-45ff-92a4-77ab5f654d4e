# Cursor 编辑器活动栏设置教程

## 文章信息
- **标题**: 如何设置 Cursor 编辑器活动栏在侧边显示
- **作者**: 却也1029
- **发布时间**: 2025-05-27
- **阅读量**: 7.2k
- **点赞数**: 29
- **收藏数**: 20

## 问题描述

在下载安装完 **Cursor** 编辑器后，用户会发现其侧边栏与 **VSCode** 的侧边栏默认样式不一致，这导致很多习惯了 VSCode 布局的用户感到不适应。

**Cursor** 默认将活动栏（Activity Bar）放置在窗口顶部，而不是像 VSCode 那样放在左侧。

## 官方解释

Cursor 官方对此设计给出了合理的解释，但考虑到开发习惯和用户体验，建议将活动栏调整回传统的侧边布局。

## 解决方案

### 详细操作步骤

#### 第一步：打开设置页面
从 `菜单栏` → `文件` → `首选项` → `设置` 打开编辑器设置页面。

#### 第二步：搜索配置项
在设置页面搜索框中输入 `workbench.activityBar.orientation`，然后选择 `vertical`。

#### 第三步：重启应用
选择完成后，**Cursor** 会提示需要重启才能生效。请确保代码更改已保存，然后点击 `【重启】` 按钮即可生效。

### 配置说明

- **配置项**: `workbench.activityBar.orientation`
- **默认值**: `horizontal` (水平，顶部显示)
- **修改值**: `vertical` (垂直，侧边显示)

## 效果展示

完成以上步骤后，活动栏将变回熟悉的侧边样式，与 VSCode 保持一致的布局。

## 相关标签

- 编辑器
- AI编程
- VSCode
- Cursor

## 总结

这个简单的配置修改可以让习惯了 VSCode 布局的开发者更快适应 Cursor 编辑器，在不影响 Cursor 强大 AI 功能的前提下，保持熟悉的界面布局。

## 扩展阅读

- Cursor 聊天窗口介绍与使用
- Cursor 配置优化
- AI 编程工具对比

---

*本文总结自 CSDN 博客文章，原文链接：https://blog.csdn.net/Khada_Finger/article/details/146158358*
