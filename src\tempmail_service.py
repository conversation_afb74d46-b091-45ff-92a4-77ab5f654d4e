"""
临时邮箱服务
"""
import requests
import time
import re
from typing import Dict, Any, Optional, List
from src.models import TempMailConfig, MailMessage
from src.exceptions import TempMailAPIError, NetworkError, VerificationCodeError


class TempMailService:
    """临时邮箱服务类"""
    
    BASE_URL = "https://tempmail.plus/api"
    
    def __init__(self, config: TempMailConfig, max_retries: int = 5, retry_interval: int = 3000):
        self.config = config
        self.max_retries = max_retries
        self.retry_interval = retry_interval / 1000  # 转换为秒
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'AugmentAutoGUI/1.0'
        })
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                     params: Optional[Dict] = None) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.BASE_URL}/{endpoint.lstrip('/')}"
        
        for attempt in range(self.max_retries):
            try:
                if method.upper() == 'GET':
                    response = self.session.get(url, params=params, timeout=10)
                elif method.upper() == 'POST':
                    response = self.session.post(url, data=data, timeout=10)
                elif method.upper() == 'DELETE':
                    response = self.session.delete(url, data=data, timeout=10)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                
                response.raise_for_status()
                
                try:
                    return response.json()
                except ValueError:
                    raise TempMailAPIError(f"API返回无效的JSON响应: {response.text}")
                
            except requests.exceptions.Timeout:
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_interval * (2 ** attempt))  # 指数退避
                    continue
                raise NetworkError("请求超时")
            
            except requests.exceptions.ConnectionError:
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_interval * (2 ** attempt))
                    continue
                raise NetworkError("网络连接失败")
            
            except requests.exceptions.HTTPError as e:
                if e.response.status_code >= 500 and attempt < self.max_retries - 1:
                    time.sleep(self.retry_interval * (2 ** attempt))
                    continue
                raise TempMailAPIError(f"API请求失败: {e.response.status_code} - {e.response.text}")
            
            except Exception as e:
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_interval)
                    continue
                raise TempMailAPIError(f"请求失败: {str(e)}")
        
        raise NetworkError(f"经过{self.max_retries}次重试后仍然失败")
    
    def get_mail_list(self, limit: int = 20) -> Dict[str, Any]:
        """获取邮件列表"""
        email = f"{self.config.username}{self.config.email_extension}"
        params = {
            'email': email,
            'limit': limit,
            'epin': self.config.epin
        }
        
        try:
            response = self._make_request('GET', '/mails', params=params)
            return response
        except Exception as e:
            raise TempMailAPIError(f"获取邮件列表失败: {str(e)}")
    
    def get_mail_detail(self, mail_id: str) -> Dict[str, Any]:
        """获取邮件详情"""
        email = f"{self.config.username}{self.config.email_extension}"
        params = {
            'email': email,
            'epin': self.config.epin
        }
        
        try:
            response = self._make_request('GET', f'/mails/{mail_id}', params=params)
            return response
        except Exception as e:
            raise TempMailAPIError(f"获取邮件详情失败: {str(e)}")
    
    def delete_mail(self, mail_id: str) -> bool:
        """删除邮件"""
        email = f"{self.config.username}{self.config.email_extension}"
        data = {
            'email': email,
            'first_id': mail_id,
            'epin': self.config.epin
        }
        
        try:
            response = self._make_request('DELETE', '/mails/', data=data)
            return response.get('result', False) is True
        except Exception as e:
            raise TempMailAPIError(f"删除邮件失败: {str(e)}")
    
    def extract_verification_code(self, mail_text: str) -> Optional[str]:
        """从邮件文本中提取验证码"""
        if not mail_text:
            return None
        
        # 匹配6位数字验证码，确保不是邮箱地址或其他格式的一部分
        pattern = r'(?<![a-zA-Z@.])\b\d{6}\b'
        match = re.search(pattern, mail_text)
        
        if match:
            return match.group(0)
        
        return None
    
    def get_latest_verification_code(self) -> Optional[str]:
        """获取最新邮件中的验证码"""
        try:
            # 获取邮件列表
            mail_list = self.get_mail_list(limit=1)
            
            if not mail_list.get('result') or not mail_list.get('first_id'):
                return None
            
            first_id = mail_list['first_id']
            
            # 获取邮件详情
            mail_detail = self.get_mail_detail(first_id)
            
            if not mail_detail.get('result'):
                return None
            
            mail_text = mail_detail.get('text', '')
            verification_code = self.extract_verification_code(mail_text)
            
            # 如果找到验证码，尝试删除邮件
            if verification_code:
                try:
                    self.delete_mail(first_id)
                except Exception:
                    # 删除失败不影响验证码获取
                    pass
            
            return verification_code
            
        except Exception as e:
            raise TempMailAPIError(f"获取验证码失败: {str(e)}")
    
    def get_verification_code_with_retry(self, max_attempts: int = None, 
                                       retry_interval: int = None) -> str:
        """带重试机制获取验证码"""
        if max_attempts is None:
            max_attempts = self.max_retries
        if retry_interval is None:
            retry_interval = self.retry_interval
        
        for attempt in range(max_attempts):
            try:
                code = self.get_latest_verification_code()
                if code:
                    return code
                
                if attempt < max_attempts - 1:
                    time.sleep(retry_interval)
                    
            except Exception as e:
                if attempt < max_attempts - 1:
                    time.sleep(retry_interval)
                    continue
                raise e
        
        raise VerificationCodeError(f"经过{max_attempts}次尝试后仍未获取到验证码")
    
    def test_connection(self) -> bool:
        """测试连接是否正常"""
        try:
            self.get_mail_list(limit=1)
            return True
        except Exception:
            return False