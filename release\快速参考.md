# 🚀 AugmentCode自动注册助手 - 快速参考

## 启动命令
```bash
python run_simple_app.py
```

## 🔐 预设账号

| 用户名 | 类型 | 邮箱限制 | 操作间隔 |
|--------|------|----------|----------|
| `trial` | 体验用户 | 1次 | 30秒 |
| `monthly_user` | 月度会员 | 4次 | 10秒 |
| `super_user` | VIP会员 | 无限制 | 5秒 |

## 📧 基本操作

### 生成邮箱
1. 设置生成数量 (1-50)
2. 点击"生成邮箱"
3. 查看生成结果

### 复制邮箱
- **双击邮箱地址** → 自动复制
- **右键菜单** → 选择"复制邮箱"

### 获取验证码
1. 选择邮箱
2. 点击"验证码获取"
3. 查看验证码结果

### 管理邮箱
- **清除单个**: 右键 → 删除选中
- **清除全部**: 点击"清除列表"

## ⚡ 快捷键

| 操作 | 快捷键 |
|------|--------|
| 复制邮箱 | 双击 |
| 右键菜单 | 右键 |
| 删除邮箱 | Delete |

## 💾 数据存储

- **邮箱数据**: `data/emails/{用户ID}_emails.json`
- **用户数据**: `data/users/users.json`
- **自动保存**: 生成后立即保存
- **自动加载**: 登录后自动恢复

## ⚠️ 权限限制

### 体验用户 (trial)
- ✅ 1次邮箱生成
- ✅ 无限验证码
- ⏱️ 30秒间隔

### 月度会员 (monthly_user)
- ✅ 4次邮箱生成
- ✅ 无限验证码
- ⏱️ 10秒间隔

### VIP会员 (super_user)
- ✅ 无限邮箱生成
- ✅ 无限验证码
- ⏱️ 5秒间隔

## 🔧 故障排除

| 问题 | 解决方案 |
|------|----------|
| 无法生成邮箱 | 检查权限限制和操作间隔 |
| 验证码获取失败 | 检查网络连接，稍后重试 |
| 邮箱数据丢失 | 重新登录，检查存储目录 |
| 登录失败 | 确认用户名或使用预设按钮 |

## 📱 界面布局

```
┌─────────────────────────────────┐
│ 用户 | 帮助                     │ ← 菜单栏
├─────────────────────────────────┤
│ [数量:1] [生成] [清除]           │ ← 控制区
├─────────────────────────────────┤
│ 序号 | 邮箱地址 | 生成时间       │ ← 邮箱列表
│  1   | xxx@xxx  | 2025-01-01   │
├─────────────────────────────────┤
│ [验证码获取]                     │ ← 验证码区
├─────────────────────────────────┤
│ 状态: 已登录 trial               │ ← 状态栏
└─────────────────────────────────┘
```

## 🎯 使用流程

1. **启动** → 运行 `python run_simple_app.py`
2. **登录** → 选择用户类型或输入用户名
3. **生成** → 设置数量，点击生成邮箱
4. **复制** → 双击邮箱地址复制到剪贴板
5. **验证** → 选择邮箱，获取验证码
6. **管理** → 清理不需要的邮箱

## 📞 支持

- **文档**: 查看完整使用指导
- **日志**: 控制台显示详细信息
- **重启**: 遇到问题可重启应用

---
**快速参考 v1.0** | **AugmentCode Team** | **2025-07-19**
