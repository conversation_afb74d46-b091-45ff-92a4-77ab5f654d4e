"""
使用统计和异常行为监控系统
"""
import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import threading


@dataclass
class UsageEvent:
    """使用事件记录"""
    user_id: str
    username: str
    event_type: str          # email_generation, verification_request, login, logout
    timestamp: datetime
    ip_address: str = ""
    user_agent: str = ""
    success: bool = True
    error_message: str = ""
    metadata: Dict = None


@dataclass
class AnomalyAlert:
    """异常警报"""
    alert_id: str
    user_id: str
    username: str
    alert_type: str          # rate_limit_exceeded, suspicious_activity, quota_exceeded
    severity: str            # low, medium, high, critical
    description: str
    timestamp: datetime
    resolved: bool = False


class UsageMonitor:
    """使用监控器"""
    
    def __init__(self, data_dir: str = "data/monitoring"):
        """初始化使用监控器"""
        self.data_dir = data_dir
        self.events_file = os.path.join(data_dir, "usage_events.json")
        self.alerts_file = os.path.join(data_dir, "anomaly_alerts.json")
        self.stats_file = os.path.join(data_dir, "usage_stats.json")
        
        # 确保数据目录存在
        os.makedirs(data_dir, exist_ok=True)
        
        # 异常检测阈值配置
        self.anomaly_thresholds = {
            'rapid_requests': 10,        # 10秒内超过此数量的请求视为异常
            'daily_quota_ratio': 0.9,    # 超过日配额90%时警告
            'failed_attempts': 5,        # 连续失败次数
            'unusual_hours': (23, 6),    # 异常时间段 (23:00-06:00)
            'max_session_duration': 8    # 最大会话时长(小时)
        }
        
        # 启动后台监控线程
        self._start_monitoring_thread()
    
    def record_event(self, user_id: str, username: str, event_type: str, 
                    success: bool = True, error_message: str = "", 
                    ip_address: str = "", user_agent: str = "", 
                    metadata: Dict = None) -> None:
        """记录使用事件"""
        event = UsageEvent(
            user_id=user_id,
            username=username,
            event_type=event_type,
            timestamp=datetime.now(),
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message,
            metadata=metadata or {}
        )
        
        self._save_event(event)
        
        # 实时异常检测
        self._check_for_anomalies(event)
    
    def _save_event(self, event: UsageEvent) -> None:
        """保存事件到文件"""
        try:
            events = []
            if os.path.exists(self.events_file):
                with open(self.events_file, 'r', encoding='utf-8') as f:
                    events = json.load(f)
            
            # 转换事件为字典
            event_dict = asdict(event)
            event_dict['timestamp'] = event.timestamp.isoformat()
            
            events.append(event_dict)
            
            # 保留最近30天的事件
            cutoff_time = datetime.now() - timedelta(days=30)
            events = [e for e in events if datetime.fromisoformat(e['timestamp']) > cutoff_time]
            
            with open(self.events_file, 'w', encoding='utf-8') as f:
                json.dump(events, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存事件失败: {e}")
    
    def _check_for_anomalies(self, event: UsageEvent) -> None:
        """检查异常行为"""
        try:
            # 检查快速请求异常
            self._check_rapid_requests(event)
            
            # 检查配额使用异常
            self._check_quota_usage(event)
            
            # 检查失败尝试异常
            self._check_failed_attempts(event)
            
            # 检查异常时间访问
            self._check_unusual_hours(event)
            
        except Exception as e:
            print(f"异常检测失败: {e}")
    
    def _check_rapid_requests(self, event: UsageEvent) -> None:
        """检查快速请求异常"""
        if event.event_type not in ['email_generation', 'verification_request']:
            return
        
        # 获取最近10秒内的同类型事件
        recent_events = self._get_recent_events(
            event.user_id, event.event_type, seconds=10
        )
        
        if len(recent_events) >= self.anomaly_thresholds['rapid_requests']:
            self._create_alert(
                event.user_id, event.username,
                'rapid_requests', 'medium',
                f"用户在10秒内进行了{len(recent_events)}次{event.event_type}操作"
            )
    
    def _check_quota_usage(self, event: UsageEvent) -> None:
        """检查配额使用异常"""
        if event.event_type not in ['email_generation', 'verification_request']:
            return
        
        # 这里需要与权限管理器集成来获取用户配额
        # 暂时跳过具体实现
        pass
    
    def _check_failed_attempts(self, event: UsageEvent) -> None:
        """检查连续失败尝试"""
        if event.success:
            return
        
        # 获取最近的失败事件
        recent_failures = self._get_recent_events(
            event.user_id, event.event_type, minutes=30, success_only=False
        )
        
        consecutive_failures = 0
        for e in reversed(recent_failures):
            if not e.get('success', True):
                consecutive_failures += 1
            else:
                break
        
        if consecutive_failures >= self.anomaly_thresholds['failed_attempts']:
            self._create_alert(
                event.user_id, event.username,
                'consecutive_failures', 'high',
                f"用户连续{consecutive_failures}次操作失败"
            )
    
    def _check_unusual_hours(self, event: UsageEvent) -> None:
        """检查异常时间访问"""
        hour = event.timestamp.hour
        start_hour, end_hour = self.anomaly_thresholds['unusual_hours']
        
        if hour >= start_hour or hour <= end_hour:
            self._create_alert(
                event.user_id, event.username,
                'unusual_hours', 'low',
                f"用户在异常时间{hour:02d}:00进行操作"
            )
    
    def _get_recent_events(self, user_id: str, event_type: str = None, 
                          seconds: int = None, minutes: int = None, 
                          hours: int = None, success_only: bool = True) -> List[Dict]:
        """获取最近的事件"""
        try:
            if not os.path.exists(self.events_file):
                return []
            
            with open(self.events_file, 'r', encoding='utf-8') as f:
                events = json.load(f)
            
            # 计算时间范围
            if seconds:
                cutoff_time = datetime.now() - timedelta(seconds=seconds)
            elif minutes:
                cutoff_time = datetime.now() - timedelta(minutes=minutes)
            elif hours:
                cutoff_time = datetime.now() - timedelta(hours=hours)
            else:
                cutoff_time = datetime.now() - timedelta(hours=1)
            
            # 过滤事件
            filtered_events = []
            for event in events:
                if (event['user_id'] == user_id and
                    datetime.fromisoformat(event['timestamp']) > cutoff_time):
                    
                    if event_type and event['event_type'] != event_type:
                        continue
                    
                    if success_only and not event.get('success', True):
                        continue
                    
                    filtered_events.append(event)
            
            return filtered_events
            
        except Exception as e:
            print(f"获取最近事件失败: {e}")
            return []
    
    def _create_alert(self, user_id: str, username: str, alert_type: str, 
                     severity: str, description: str) -> None:
        """创建异常警报"""
        alert = AnomalyAlert(
            alert_id=f"{user_id}_{alert_type}_{int(time.time())}",
            user_id=user_id,
            username=username,
            alert_type=alert_type,
            severity=severity,
            description=description,
            timestamp=datetime.now()
        )
        
        self._save_alert(alert)
    
    def _save_alert(self, alert: AnomalyAlert) -> None:
        """保存警报到文件"""
        try:
            alerts = []
            if os.path.exists(self.alerts_file):
                with open(self.alerts_file, 'r', encoding='utf-8') as f:
                    alerts = json.load(f)
            
            # 转换警报为字典
            alert_dict = asdict(alert)
            alert_dict['timestamp'] = alert.timestamp.isoformat()
            
            alerts.append(alert_dict)
            
            # 保留最近7天的警报
            cutoff_time = datetime.now() - timedelta(days=7)
            alerts = [a for a in alerts if datetime.fromisoformat(a['timestamp']) > cutoff_time]
            
            with open(self.alerts_file, 'w', encoding='utf-8') as f:
                json.dump(alerts, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存警报失败: {e}")
    
    def get_usage_statistics(self, days: int = 7) -> Dict:
        """获取使用统计"""
        try:
            if not os.path.exists(self.events_file):
                return {}
            
            with open(self.events_file, 'r', encoding='utf-8') as f:
                events = json.load(f)
            
            # 过滤指定天数内的事件
            cutoff_time = datetime.now() - timedelta(days=days)
            recent_events = [
                e for e in events 
                if datetime.fromisoformat(e['timestamp']) > cutoff_time
            ]
            
            # 统计数据
            stats = {
                'total_events': len(recent_events),
                'unique_users': len(set(e['user_id'] for e in recent_events)),
                'event_types': Counter(e['event_type'] for e in recent_events),
                'success_rate': sum(1 for e in recent_events if e.get('success', True)) / len(recent_events) if recent_events else 0,
                'hourly_distribution': defaultdict(int),
                'daily_distribution': defaultdict(int),
                'user_activity': defaultdict(int)
            }
            
            # 按小时和日期分布
            for event in recent_events:
                timestamp = datetime.fromisoformat(event['timestamp'])
                stats['hourly_distribution'][timestamp.hour] += 1
                stats['daily_distribution'][timestamp.date().isoformat()] += 1
                stats['user_activity'][event['username']] += 1
            
            return stats
            
        except Exception as e:
            print(f"获取使用统计失败: {e}")
            return {}
    
    def get_active_alerts(self) -> List[Dict]:
        """获取活跃警报"""
        try:
            if not os.path.exists(self.alerts_file):
                return []
            
            with open(self.alerts_file, 'r', encoding='utf-8') as f:
                alerts = json.load(f)
            
            # 返回未解决的警报
            return [a for a in alerts if not a.get('resolved', False)]
            
        except Exception as e:
            print(f"获取活跃警报失败: {e}")
            return []
    
    def resolve_alert(self, alert_id: str) -> bool:
        """解决警报"""
        try:
            if not os.path.exists(self.alerts_file):
                return False
            
            with open(self.alerts_file, 'r', encoding='utf-8') as f:
                alerts = json.load(f)
            
            # 标记警报为已解决
            for alert in alerts:
                if alert['alert_id'] == alert_id:
                    alert['resolved'] = True
                    break
            
            with open(self.alerts_file, 'w', encoding='utf-8') as f:
                json.dump(alerts, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"解决警报失败: {e}")
            return False
    
    def _start_monitoring_thread(self):
        """启动后台监控线程"""
        def monitor_loop():
            while True:
                try:
                    # 每小时执行一次清理和统计更新
                    self._cleanup_old_data()
                    self._update_statistics()
                    time.sleep(3600)  # 1小时
                except Exception as e:
                    print(f"监控线程错误: {e}")
                    time.sleep(60)  # 出错时等待1分钟后重试
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
    
    def _cleanup_old_data(self):
        """清理旧数据"""
        # 清理超过30天的事件
        if os.path.exists(self.events_file):
            try:
                with open(self.events_file, 'r', encoding='utf-8') as f:
                    events = json.load(f)
                
                cutoff_time = datetime.now() - timedelta(days=30)
                events = [e for e in events if datetime.fromisoformat(e['timestamp']) > cutoff_time]
                
                with open(self.events_file, 'w', encoding='utf-8') as f:
                    json.dump(events, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"清理事件数据失败: {e}")
        
        # 清理超过7天的警报
        if os.path.exists(self.alerts_file):
            try:
                with open(self.alerts_file, 'r', encoding='utf-8') as f:
                    alerts = json.load(f)
                
                cutoff_time = datetime.now() - timedelta(days=7)
                alerts = [a for a in alerts if datetime.fromisoformat(a['timestamp']) > cutoff_time]
                
                with open(self.alerts_file, 'w', encoding='utf-8') as f:
                    json.dump(alerts, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"清理警报数据失败: {e}")
    
    def _update_statistics(self):
        """更新统计数据"""
        try:
            stats = self.get_usage_statistics(days=30)  # 获取30天统计
            
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"更新统计数据失败: {e}")


# 全局监控器实例
_usage_monitor = None


def get_usage_monitor() -> UsageMonitor:
    """获取使用监控器实例"""
    global _usage_monitor
    if _usage_monitor is None:
        _usage_monitor = UsageMonitor()
    return _usage_monitor
