"""
日志管理器
"""
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import List, Callable, Optional
from src.models import LogEntry


class LogManager:
    """日志管理器"""
    
    LOG_LEVELS = {
        'INFO': '#202124',      # 深灰色
        'SUCCESS': '#1e8e3e',   # 绿色
        'WARNING': '#ea8600',   # 橙色
        'ERROR': '#d93025'      # 红色
    }
    
    LOG_BACKGROUNDS = {
        'INFO': '#f8f9fa',      # 浅灰色
        'SUCCESS': '#e6f4ea',   # 浅绿色
        'WARNING': '#fef7e0',   # 浅黄色
        'ERROR': '#fce8e6'      # 浅红色
    }
    
    def __init__(self, max_entries: int = 1000):
        self.max_entries = max_entries
        self.log_entries: List[LogEntry] = []
        self.callbacks: List[Callable[[LogEntry], None]] = []
        self.log_file_path = self._get_log_file_path()
        self._setup_file_logging()
    
    def _get_log_file_path(self) -> Path:
        """获取日志文件路径"""
        if os.name == 'nt':  # Windows
            log_dir = Path(os.environ.get('APPDATA', '')) / "AugmentAutoGUI" / "logs"
        else:  # macOS/Linux
            log_dir = Path.home() / ".augment_auto_gui" / "logs"
        
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 使用日期作为文件名
        today = datetime.now().strftime("%Y-%m-%d")
        return log_dir / f"app_{today}.log"
    
    def _setup_file_logging(self):
        """设置文件日志"""
        # 创建文件处理器
        file_handler = logging.FileHandler(self.log_file_path, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        
        # 获取或创建logger
        self.file_logger = logging.getLogger('AugmentAutoGUI')
        self.file_logger.setLevel(logging.INFO)
        
        # 清除现有处理器，避免重复
        self.file_logger.handlers.clear()
        self.file_logger.addHandler(file_handler)
    
    def add_log(self, message: str, level: str = 'INFO') -> None:
        """添加日志条目"""
        if level not in self.LOG_LEVELS:
            level = 'INFO'
        
        # 创建日志条目
        log_entry = LogEntry(
            timestamp=datetime.now(),
            level=level,
            message=message
        )
        
        # 添加到内存列表
        self.log_entries.append(log_entry)
        
        # 限制内存中的日志数量
        if len(self.log_entries) > self.max_entries:
            self.log_entries = self.log_entries[-self.max_entries:]
        
        # 写入文件
        self._write_to_file(log_entry)
        
        # 通知回调函数
        for callback in self.callbacks:
            try:
                callback(log_entry)
            except Exception:
                # 忽略回调函数中的错误，避免影响日志记录
                pass
    
    def _write_to_file(self, log_entry: LogEntry):
        """写入日志到文件"""
        try:
            # 映射自定义级别到标准日志级别
            level_mapping = {
                'INFO': logging.INFO,
                'SUCCESS': logging.INFO,
                'WARNING': logging.WARNING,
                'ERROR': logging.ERROR
            }
            
            log_level = level_mapping.get(log_entry.level, logging.INFO)
            formatted_message = f"[{log_entry.level}] {log_entry.message}"
            
            self.file_logger.log(log_level, formatted_message)
            
        except Exception:
            # 文件写入失败不应该影响程序运行
            pass
    
    def clear_logs(self) -> None:
        """清除内存中的日志"""
        self.log_entries.clear()
        
        # 通知回调函数日志已清除
        for callback in self.callbacks:
            try:
                callback(None)  # None表示清除操作
            except Exception:
                pass
    
    def get_logs(self, level_filter: Optional[str] = None, 
                 limit: Optional[int] = None) -> List[LogEntry]:
        """获取日志条目"""
        logs = self.log_entries
        
        # 按级别过滤
        if level_filter and level_filter in self.LOG_LEVELS:
            logs = [log for log in logs if log.level == level_filter]
        
        # 限制数量
        if limit and limit > 0:
            logs = logs[-limit:]
        
        return logs
    
    def export_logs(self, file_path: Optional[str] = None) -> str:
        """导出日志到文件"""
        if not file_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = f"logs_export_{timestamp}.txt"
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("AugmentAutoGUI 日志导出\n")
                f.write("=" * 50 + "\n\n")
                
                for log_entry in self.log_entries:
                    timestamp_str = log_entry.timestamp.strftime("%Y-%m-%d %H:%M:%S")
                    f.write(f"[{timestamp_str}] [{log_entry.level}] {log_entry.message}\n")
            
            return file_path
            
        except Exception as e:
            raise Exception(f"导出日志失败: {str(e)}")
    
    def add_callback(self, callback: Callable[[LogEntry], None]) -> None:
        """添加日志回调函数"""
        if callback not in self.callbacks:
            self.callbacks.append(callback)
    
    def remove_callback(self, callback: Callable[[LogEntry], None]) -> None:
        """移除日志回调函数"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def get_log_color(self, level: str) -> tuple:
        """获取日志级别对应的颜色"""
        text_color = self.LOG_LEVELS.get(level, self.LOG_LEVELS['INFO'])
        bg_color = self.LOG_BACKGROUNDS.get(level, self.LOG_BACKGROUNDS['INFO'])
        return text_color, bg_color
    
    def get_log_stats(self) -> dict:
        """获取日志统计信息"""
        stats = {level: 0 for level in self.LOG_LEVELS.keys()}
        
        for log_entry in self.log_entries:
            if log_entry.level in stats:
                stats[log_entry.level] += 1
        
        stats['total'] = len(self.log_entries)
        return stats