'''
Author: ya<PERSON><PERSON>
Date: 2025-07-18 11:21:02
LastEditTime: 2025-07-18 19:12:13
LastEditors: yangyang
Description: 
FilePath: \Augment\run_simple_app.py
牛马人生，只希望活得长一点
'''
#!/usr/bin/env python3
"""
邮箱验证码接收助手启动脚本
只包含邮箱生成和验证码获取功能
已隐藏用户登录、权限管理等功能
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from src.app import Application


def main():
    """主函数"""
    print("启动邮箱验证码接收助手...")
    print("=" * 60)
    print("当前版本功能:")
    print("• 📧 邮箱生成: 自动生成临时邮箱地址")
    print("• 🔐 验证码获取: 从tempmail.plus获取验证码")
    print("• ⚙️  配置已预设: 无需手动配置")
    print("• 🚫 已隐藏: 用户登录、权限管理等功能")
    print("")
    print("注意: 登录界面等其他功能已暂时隐藏(注释)")
    print("=" * 60)
    
    try:
        # 创建应用实例
        app = Application()
        
        # 运行应用
        app.run()
        
    except KeyboardInterrupt:
        print("\n用户中断，程序退出")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")


if __name__ == "__main__":
    main()
