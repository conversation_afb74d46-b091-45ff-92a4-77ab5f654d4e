@echo off
chcp 65001 >nul
echo 开始打包 AugmentCode自动注册助手...
echo.

REM 清理旧文件
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

REM 快速打包
pyinstaller --onefile --windowed --name "AugmentCode自动注册助手" --add-data "data;data" run_simple_app.py

if exist "dist\AugmentCode自动注册助手.exe" (
    echo.
    echo 打包成功！
    echo 可执行文件：dist\AugmentCode自动注册助手.exe
    echo.
    
    REM 创建发布目录
    if not exist "release" mkdir "release"
    copy "dist\AugmentCode自动注册助手.exe" "release\"
    
    echo 发布文件已复制到 release 目录
) else (
    echo 打包失败！
)

pause
