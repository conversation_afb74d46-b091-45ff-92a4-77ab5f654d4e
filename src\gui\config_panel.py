"""
配置设置面板
"""
import tkinter as tk
from tkinter import ttk, messagebox
from src.models import TempMailConfig, AppConfig
from src.gui.theme_manager import theme_manager
from src.gui.custom_widgets import ModernButton, ModernEntry
from src.gui.layout_manager import LayoutManager


class ConfigPanel:
    """配置设置面板"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self._is_compact_mode = False

        self._create_widgets()
        self._load_config()
    
    def _create_widgets(self):
        """创建界面组件"""
        self.frame = ttk.Frame(self.parent)
        self.frame.configure(style='TFrame')
        
        # 创建标题区域
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = ttk.Label(title_frame, text="配置设置", 
                               style='Title.TLabel')
        title_label.pack(anchor='center')
        
        # 创建说明文本区域
        info_frame = ttk.Frame(self.frame)
        info_frame.pack(fill=tk.X, padx=20, pady=(0, 15))
        
        info_text = "请在此设置tempmail.plus的连接参数和应用程序选项。"
        info_label = ttk.Label(info_frame, text=info_text, 
                              justify=tk.CENTER,
                              wraplength=600)
        info_label.pack(anchor='center')
        
        # 创建tempmail连接设置区域
        tempmail_frame = LayoutManager.create_section_frame(self.frame, "tempmail.plus 连接设置")
        tempmail_grid = ttk.Frame(tempmail_frame)
        tempmail_grid.pack(fill=tk.X, padx=15, pady=15)
        
        # 用户名
        username_frame, username_entry = LayoutManager.create_form_field(
            tempmail_grid, "用户名:", ttk.Entry, 
            {"width": 30}
        )
        username_frame.grid(row=0, column=0, sticky=tk.W, pady=8, padx=5)
        self.username_var = tk.StringVar()
        username_entry.config(textvariable=self.username_var)
        
        # 邮箱扩展名
        email_ext_frame = ttk.Frame(tempmail_grid)
        email_ext_frame.grid(row=1, column=0, sticky=tk.W, pady=8, padx=5)
        
        ttk.Label(email_ext_frame, text="邮箱扩展名:").pack(side=tk.TOP, anchor='w')
        
        ext_input_frame = ttk.Frame(email_ext_frame)
        ext_input_frame.pack(side=tk.TOP, fill=tk.X, pady=(2, 0))
        
        self.email_ext_var = tk.StringVar()
        email_ext_entry = ttk.Entry(ext_input_frame, textvariable=self.email_ext_var, width=30)
        email_ext_entry.pack(side=tk.LEFT)
        
        ttk.Label(ext_input_frame, text="(例如: @fexpost.com)", 
                 style='Small.TLabel').pack(side=tk.LEFT, padx=(5, 0))
        
        # epin
        epin_frame = ttk.Frame(tempmail_grid)
        epin_frame.grid(row=2, column=0, sticky=tk.W, pady=8, padx=5)
        
        ttk.Label(epin_frame, text="epin:").pack(side=tk.TOP, anchor='w')
        
        epin_input_frame = ttk.Frame(epin_frame)
        epin_input_frame.pack(side=tk.TOP, fill=tk.X, pady=(2, 0))
        
        self.epin_var = tk.StringVar()
        self.epin_entry = ttk.Entry(epin_input_frame, textvariable=self.epin_var, width=30, show="*")
        self.epin_entry.pack(side=tk.LEFT)
        
        # 显示/隐藏epin
        self.show_epin_var = tk.BooleanVar(value=False)
        show_epin_cb = ttk.Checkbutton(epin_input_frame, text="显示epin", 
                                      variable=self.show_epin_var,
                                      command=self._toggle_epin_visibility)
        show_epin_cb.pack(side=tk.LEFT, padx=(5, 0))
        
        # 创建应用设置区域
        app_frame = LayoutManager.create_section_frame(self.frame, "应用设置")
        app_grid = ttk.Frame(app_frame)
        app_grid.pack(fill=tk.X, padx=15, pady=15)
        
        # 邮箱域名
        domain_frame = ttk.Frame(app_grid)
        domain_frame.grid(row=0, column=0, sticky=tk.W, pady=8, padx=5)
        
        ttk.Label(domain_frame, text="邮箱域名:").pack(side=tk.TOP, anchor='w')
        
        domain_input_frame = ttk.Frame(domain_frame)
        domain_input_frame.pack(side=tk.TOP, fill=tk.X, pady=(2, 0))
        
        self.email_domain_var = tk.StringVar()
        email_domain_entry = ttk.Entry(domain_input_frame, textvariable=self.email_domain_var, width=30)
        email_domain_entry.pack(side=tk.LEFT)
        
        ttk.Label(domain_input_frame, text="(例如: @465447.xyz)", 
                 style='Small.TLabel').pack(side=tk.LEFT, padx=(5, 0))
        
        # 最大重试次数
        retries_frame, max_retries_spinbox = LayoutManager.create_form_field(
            app_grid, "最大重试次数:", ttk.Spinbox, 
            {"from_": 1, "to": 10, "width": 5}
        )
        retries_frame.grid(row=1, column=0, sticky=tk.W, pady=8, padx=5)
        self.max_retries_var = tk.StringVar()
        max_retries_spinbox.config(textvariable=self.max_retries_var)
        
        # 重试间隔
        interval_frame, retry_interval_spinbox = LayoutManager.create_form_field(
            app_grid, "重试间隔(毫秒):", ttk.Spinbox, 
            {"from_": 1000, "to": 10000, "increment": 500, "width": 7}
        )
        interval_frame.grid(row=2, column=0, sticky=tk.W, pady=8, padx=5)
        self.retry_interval_var = tk.StringVar()
        retry_interval_spinbox.config(textvariable=self.retry_interval_var)
        
        # 按钮区域
        button_frame = ttk.Frame(self.frame)
        button_frame.pack(fill=tk.X, padx=20, pady=(15, 10))
        
        # 创建按钮
        save_btn = ModernButton(button_frame, text="保存配置", 
                              command=self._on_save_click)
        
        reset_btn = ModernButton(button_frame, text="重置为默认", 
                               style='Secondary.TButton',
                               command=self._on_reset_click)
        
        # 使用布局管理器创建按钮组
        buttons = [save_btn, reset_btn]
        button_group = LayoutManager.create_button_group(button_frame, buttons)
        button_group.pack(anchor='center')
        
        # 提示信息
        tip_frame = ttk.Frame(self.frame)
        tip_frame.pack(fill=tk.X, padx=20, pady=(10, 15))
        
        tip_label = ttk.Label(tip_frame, 
                            text="提示: 请在tempmail.plus网站注册账号后，填写对应的连接参数",
                            style='Small.TLabel')
        tip_label.pack(anchor='center')
    
    def _toggle_epin_visibility(self):
        """切换epin显示/隐藏"""
        show = self.show_epin_var.get()
        self.epin_entry.config(show="" if show else "*")
    
    def _load_config(self):
        """加载配置到界面"""
        if self.main_window.app.app_config:
            # 加载tempmail配置
            self.username_var.set(self.main_window.app.app_config.temp_mail.username)
            self.email_ext_var.set(self.main_window.app.app_config.temp_mail.email_extension)
            self.epin_var.set(self.main_window.app.app_config.temp_mail.epin)
            
            # 加载应用配置
            self.email_domain_var.set(self.main_window.app.app_config.email_domain)
            self.max_retries_var.set(str(self.main_window.app.app_config.max_retries))
            self.retry_interval_var.set(str(self.main_window.app.app_config.retry_interval))
    
    def _on_save_click(self):
        """保存按钮点击事件"""
        try:
            # 获取表单数据
            username = self.username_var.get().strip()
            email_ext = self.email_ext_var.get().strip()
            epin = self.epin_var.get().strip()
            
            email_domain = self.email_domain_var.get().strip()
            
            try:
                max_retries = int(self.max_retries_var.get())
                retry_interval = int(self.retry_interval_var.get())
            except ValueError:
                messagebox.showerror("错误", "重试次数和间隔必须是有效的数字")
                return
            
            # 创建配置对象
            temp_mail_config = TempMailConfig(
                username=username,
                email_extension=email_ext,
                epin=epin
            )
            
            app_config = AppConfig(
                temp_mail=temp_mail_config,
                email_domain=email_domain,
                max_retries=max_retries,
                retry_interval=retry_interval
            )
            
            # 保存配置
            if self.main_window.save_config(app_config):
                # 显示成功消息
                self.main_window.status_bar.set_status("配置已成功保存", "SUCCESS")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
    
    def _on_reset_click(self):
        """重置按钮点击事件"""
        result = messagebox.askyesno("确认", "确定要重置为默认配置吗？")
        if result:
            default_config = self.main_window.app.config_manager.get_default_config()
            
            # 更新界面
            self.username_var.set(default_config.temp_mail.username)
            self.email_ext_var.set(default_config.temp_mail.email_extension)
            self.epin_var.set(default_config.temp_mail.epin)
            
            self.email_domain_var.set(default_config.email_domain)
            self.max_retries_var.set(str(default_config.max_retries))
            self.retry_interval_var.set(str(default_config.retry_interval))
            
            self.main_window.app.log_manager.add_log("已重置为默认配置", "INFO")

    def set_compact_mode(self, is_compact):
        """设置紧凑模式"""
        if self._is_compact_mode == is_compact:
            return

        self._is_compact_mode = is_compact

        if is_compact:
            self._apply_compact_layout()
        else:
            self._apply_normal_layout()

    def _apply_compact_layout(self):
        """应用紧凑布局"""
        # 减少内边距
        for child in self.frame.winfo_children():
            if isinstance(child, ttk.Frame):
                child.pack_configure(padx=10, pady=5)

    def _apply_normal_layout(self):
        """应用正常布局"""
        # 恢复内边距
        for child in self.frame.winfo_children():
            if isinstance(child, ttk.Frame):
                child.pack_configure(padx=20, pady=10)
            self.main_window.status_bar.set_status("已重置为默认配置", "INFO")