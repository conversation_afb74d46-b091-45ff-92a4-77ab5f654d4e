"""
邮箱存储管理器 - 负责邮箱数据的持久化保存和加载
"""
import json
import os
from datetime import datetime
from typing import List, Dict, Optional
from src.models import EmailInfo


class EmailStorageManager:
    """邮箱存储管理器"""
    
    def __init__(self, storage_dir: str = "data/emails"):
        """
        初始化邮箱存储管理器
        
        Args:
            storage_dir: 存储目录路径
        """
        self.storage_dir = storage_dir
        self._ensure_storage_dir()
    
    def _ensure_storage_dir(self):
        """确保存储目录存在"""
        if not os.path.exists(self.storage_dir):
            os.makedirs(self.storage_dir, exist_ok=True)
    
    def _get_user_email_file(self, user_id: str) -> str:
        """获取用户邮箱文件路径"""
        return os.path.join(self.storage_dir, f"{user_id}_emails.json")
    
    def save_user_emails(self, user_id: str, emails: List[EmailInfo]) -> bool:
        """
        保存用户的邮箱列表
        
        Args:
            user_id: 用户ID
            emails: 邮箱列表
            
        Returns:
            bool: 保存是否成功
        """
        try:
            file_path = self._get_user_email_file(user_id)
            
            # 转换为可序列化的格式
            email_data = []
            for email in emails:
                email_dict = {
                    "address": email.address,
                    "generated_time": email.generated_time.isoformat()
                }
                email_data.append(email_dict)
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump({
                    "user_id": user_id,
                    "last_updated": datetime.now().isoformat(),
                    "email_count": len(emails),
                    "emails": email_data
                }, f, ensure_ascii=False, indent=2)
            
            print(f"用户 {user_id} 的邮箱数据已保存，共 {len(emails)} 个邮箱")
            return True
            
        except Exception as e:
            print(f"保存用户邮箱数据失败: {e}")
            return False
    
    def load_user_emails(self, user_id: str) -> List[EmailInfo]:
        """
        加载用户的邮箱列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            List[EmailInfo]: 邮箱列表
        """
        try:
            file_path = self._get_user_email_file(user_id)
            
            if not os.path.exists(file_path):
                print(f"用户 {user_id} 的邮箱文件不存在")
                return []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 转换为EmailInfo对象
            emails = []
            for email_dict in data.get("emails", []):
                email_info = EmailInfo(
                    address=email_dict["address"],
                    generated_time=datetime.fromisoformat(email_dict["generated_time"])
                )
                emails.append(email_info)
            
            print(f"用户 {user_id} 的邮箱数据已加载，共 {len(emails)} 个邮箱")
            return emails
            
        except Exception as e:
            print(f"加载用户邮箱数据失败: {e}")
            return []
    
    def add_user_email(self, user_id: str, email: EmailInfo) -> bool:
        """
        为用户添加单个邮箱（追加模式）
        
        Args:
            user_id: 用户ID
            email: 邮箱信息
            
        Returns:
            bool: 添加是否成功
        """
        try:
            # 先加载现有邮箱
            existing_emails = self.load_user_emails(user_id)
            
            # 检查是否已存在相同邮箱
            for existing_email in existing_emails:
                if existing_email.address == email.address:
                    print(f"邮箱 {email.address} 已存在，跳过添加")
                    return True
            
            # 添加新邮箱
            existing_emails.append(email)
            
            # 保存更新后的列表
            return self.save_user_emails(user_id, existing_emails)
            
        except Exception as e:
            print(f"添加用户邮箱失败: {e}")
            return False
    
    def add_user_emails(self, user_id: str, emails: List[EmailInfo]) -> bool:
        """
        为用户批量添加邮箱（追加模式）
        
        Args:
            user_id: 用户ID
            emails: 邮箱列表
            
        Returns:
            bool: 添加是否成功
        """
        try:
            # 先加载现有邮箱
            existing_emails = self.load_user_emails(user_id)
            existing_addresses = {email.address for email in existing_emails}
            
            # 过滤掉重复的邮箱
            new_emails = []
            for email in emails:
                if email.address not in existing_addresses:
                    new_emails.append(email)
                    existing_addresses.add(email.address)
            
            if new_emails:
                # 添加新邮箱
                existing_emails.extend(new_emails)
                
                # 保存更新后的列表
                result = self.save_user_emails(user_id, existing_emails)
                if result:
                    print(f"为用户 {user_id} 新增 {len(new_emails)} 个邮箱")
                return result
            else:
                print(f"用户 {user_id} 没有新邮箱需要添加")
                return True
                
        except Exception as e:
            print(f"批量添加用户邮箱失败: {e}")
            return False
    
    def clear_user_emails(self, user_id: str) -> bool:
        """
        清除用户的所有邮箱
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 清除是否成功
        """
        try:
            file_path = self._get_user_email_file(user_id)
            
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"用户 {user_id} 的邮箱数据已清除")
            
            return True
            
        except Exception as e:
            print(f"清除用户邮箱数据失败: {e}")
            return False
    
    def get_user_email_count(self, user_id: str) -> int:
        """
        获取用户的邮箱数量
        
        Args:
            user_id: 用户ID
            
        Returns:
            int: 邮箱数量
        """
        try:
            file_path = self._get_user_email_file(user_id)
            
            if not os.path.exists(file_path):
                return 0
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return data.get("email_count", 0)
            
        except Exception as e:
            print(f"获取用户邮箱数量失败: {e}")
            return 0
    
    def list_user_files(self) -> List[str]:
        """
        列出所有用户的邮箱文件
        
        Returns:
            List[str]: 用户ID列表
        """
        try:
            if not os.path.exists(self.storage_dir):
                return []
            
            user_ids = []
            for filename in os.listdir(self.storage_dir):
                if filename.endswith("_emails.json"):
                    user_id = filename.replace("_emails.json", "")
                    user_ids.append(user_id)
            
            return user_ids
            
        except Exception as e:
            print(f"列出用户文件失败: {e}")
            return []
