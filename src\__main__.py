"""
应用程序入口点
"""
import sys
import os
import traceback
from tkinter import messagebox

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

# 导入应用程序
from src.app import Application


def main():
    """主函数"""
    try:
        app = Application()
        app.run()
    except Exception as e:
        # 捕获未处理的异常
        error_msg = f"应用程序发生未处理的异常:\n{str(e)}\n\n{traceback.format_exc()}"
        print(error_msg)
        
        try:
            messagebox.showerror("严重错误", error_msg)
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()