#!/usr/bin/env python3
"""
字体大小对比测试工具
用于对比优化前后的字体大小效果
"""
import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from src.gui.theme_manager import theme_manager


class FontSizeComparisonWindow:
    """字体大小对比测试窗口"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("字体大小对比测试")
        self.root.geometry("1000x700")
        
        # 应用主题
        self.style = theme_manager.apply_theme(self.root)
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建对比界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="字体大小优化对比测试", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # 系统信息
        info_frame = ttk.LabelFrame(main_frame, text="系统信息")
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        system_info = f"操作系统: {theme_manager.system}\n"
        system_info += f"DPI缩放: {theme_manager.dpi_scale:.2f}\n"
        system_info += f"系统字体: {theme_manager.system_font}\n"
        system_info += f"等宽字体: {theme_manager.monospace_font}"
        
        info_label = ttk.Label(info_frame, text=system_info, justify=tk.LEFT)
        info_label.pack(padx=15, pady=10, anchor='w')
        
        # 创建对比框架
        comparison_frame = ttk.Frame(main_frame)
        comparison_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：优化前
        old_frame = ttk.LabelFrame(comparison_frame, text="优化前（原始字体大小）")
        old_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # 右侧：优化后
        new_frame = ttk.LabelFrame(comparison_frame, text="优化后（当前字体大小）")
        new_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # 字体大小对比数据
        font_comparisons = [
            ("标题字体", "这是标题字体测试", 18, 22),
            ("副标题字体", "这是副标题字体测试", 14, 16),
            ("正文字体", "这是正文字体测试\n支持中英文混合显示", 11, 13),
            ("小字体", "这是小字体测试", 10, 12),
            ("按钮字体", "按钮文字", 11, 13),
            ("验证码字体", "123456", 16, 18),
            ("代码字体", "print('Hello')", 11, 12),
        ]
        
        # 创建对比内容
        for i, (name, text, old_size, new_size) in enumerate(font_comparisons):
            # 优化前
            old_test_frame = ttk.Frame(old_frame)
            old_test_frame.pack(fill=tk.X, padx=10, pady=8)
            
            old_name_label = ttk.Label(old_test_frame, text=f"{name}:", width=12, anchor='w')
            old_name_label.pack(side=tk.LEFT, padx=(0, 10))
            
            # 使用固定的旧字体大小
            old_font = (theme_manager.system_font, old_size, 'bold' if 'title' in name.lower() else 'normal')
            old_sample_label = ttk.Label(old_test_frame, text=text, font=old_font)
            old_sample_label.pack(side=tk.LEFT, anchor='w')
            
            # 优化后
            new_test_frame = ttk.Frame(new_frame)
            new_test_frame.pack(fill=tk.X, padx=10, pady=8)
            
            new_name_label = ttk.Label(new_test_frame, text=f"{name}:", width=12, anchor='w')
            new_name_label.pack(side=tk.LEFT, padx=(0, 10))
            
            # 使用当前主题的字体
            if 'title' in name.lower():
                style_name = 'Title.TLabel'
            elif 'subtitle' in name.lower():
                style_name = 'Subtitle.TLabel'
            elif 'small' in name.lower():
                style_name = 'Small.TLabel'
            elif 'code' in name.lower():
                style_name = 'Code.TLabel'
            elif 'verification' in name.lower():
                style_name = 'VerificationCode.TLabel'
            else:
                style_name = 'TLabel'
            
            new_sample_label = ttk.Label(new_test_frame, text=text, style=style_name)
            new_sample_label.pack(side=tk.LEFT, anchor='w')
        
        # 字体大小统计
        stats_frame = ttk.LabelFrame(main_frame, text="字体大小改进统计")
        stats_frame.pack(fill=tk.X, pady=(20, 0))
        
        stats_text = "字体大小改进幅度:\n"
        stats_text += "• 标题字体: 18px → 22px (+22%)\n"
        stats_text += "• 副标题字体: 14px → 16px (+14%)\n"
        stats_text += "• 正文字体: 11px → 13px (+18%)\n"
        stats_text += "• 小字体: 10px → 12px (+20%)\n"
        stats_text += "• 按钮字体: 11px → 13px (+18%)\n"
        stats_text += "• 验证码字体: 16px → 18px (+12%)\n"
        stats_text += "• 代码字体: 11px → 12px (+9%)\n\n"
        stats_text += f"DPI缩放系数: {theme_manager.dpi_scale:.2f}x\n"
        stats_text += "最小字体限制: 已设置各类型字体的最小尺寸"
        
        stats_label = ttk.Label(stats_frame, text=stats_text, justify=tk.LEFT)
        stats_label.pack(padx=15, pady=10, anchor='w')
        
        # 控件测试对比
        widget_frame = ttk.LabelFrame(main_frame, text="控件字体对比测试")
        widget_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 按钮对比
        button_frame = ttk.Frame(widget_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(button_frame, text="按钮对比:", width=15, anchor='w').pack(side=tk.LEFT)
        
        # 旧样式按钮（模拟）
        old_button = tk.Button(button_frame, text="优化前按钮", 
                              font=(theme_manager.system_font, 10),
                              bg='#4a6baf', fg='white', relief='flat',
                              padx=8, pady=4)
        old_button.pack(side=tk.LEFT, padx=5)
        
        # 新样式按钮
        new_button = ttk.Button(button_frame, text="优化后按钮")
        new_button.pack(side=tk.LEFT, padx=5)
        
        # 输入框对比
        entry_frame = ttk.Frame(widget_frame)
        entry_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(entry_frame, text="输入框对比:", width=15, anchor='w').pack(side=tk.LEFT)
        
        # 旧样式输入框
        old_entry = tk.Entry(entry_frame, font=(theme_manager.system_font, 10), width=20)
        old_entry.pack(side=tk.LEFT, padx=5)
        old_entry.insert(0, "优化前输入框")
        
        # 新样式输入框
        new_entry = ttk.Entry(entry_frame, width=20)
        new_entry.pack(side=tk.LEFT, padx=5)
        new_entry.insert(0, "优化后输入框")
        
        # 底部说明
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=(15, 0))
        
        explanation = ("字体优化说明:\n"
                      "1. 显著增加了所有字体的基础大小，提升可读性\n"
                      "2. 设置了各类型字体的最小尺寸限制，确保在任何DPI下都清晰\n"
                      "3. 增加了控件的内边距，适应更大的字体\n"
                      "4. 保持了字体的层次结构和视觉平衡")
        
        ttk.Label(bottom_frame, text=explanation, style='Small.TLabel', 
                 wraplength=900, justify=tk.LEFT).pack(anchor='w')
    
    def run(self):
        """运行对比测试窗口"""
        self.root.mainloop()


def main():
    """主函数"""
    print("启动字体大小对比测试...")
    print("=" * 60)
    print("对比内容:")
    print("• 优化前后字体大小对比")
    print("• 各种字体样式效果展示")
    print("• 控件字体渲染对比")
    print("• 字体改进统计数据")
    print("=" * 60)
    
    try:
        comparison_window = FontSizeComparisonWindow()
        comparison_window.run()
    except Exception as e:
        print(f"对比测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
