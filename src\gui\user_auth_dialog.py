"""
用户认证对话框
提供用户登录、注册和权限管理界面
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.user_management.user_permission_system import UserPermissionManager, UserRole
from src.gui.theme_manager import theme_manager


class UserAuthDialog:
    """用户认证对话框"""
    
    def __init__(self, parent=None):
        """初始化认证对话框"""
        self.parent = parent
        self.permission_manager = UserPermissionManager()
        self.result = None
        self.user_info = None
        
        # 创建对话框窗口 - 大幅增大尺寸确保内容完整显示
        self.dialog = tk.Toplevel(parent) if parent else tk.Tk()
        self.dialog.title("用户认证")
        self.dialog.geometry("500x900")  # 大幅增大尺寸 (700x550 → 900x750)
        self.dialog.resizable(True, True)  # 允许调整大小
        
        # 应用主题
        theme_manager.apply_theme(self.dialog)
        
        # 设置模态
        if parent:
            self.dialog.transient(parent)
            self.dialog.grab_set()
        
        self._create_widgets()
        self._center_window()
    
    def _center_window(self):
        """居中显示窗口"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建主框架 - 进一步增大边距适应更大窗口
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)  # 35 → 40
        
        # 标题
        title_label = ttk.Label(main_frame, text="用户认证", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # 创建选项卡 - 增加底部间距
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 30))  # 20 → 30
        
        # 登录选项卡
        self.login_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.login_frame, text="用户登录")
        self._create_login_tab()
        
        # # 注册选项卡 - 暂时注释掉
        # self.register_frame = ttk.Frame(self.notebook)
        # self.notebook.add(self.register_frame, text="用户注册")
        # self._create_register_tab()
        
        # 访客模式选项卡 - 已注释
        # self.guest_frame = ttk.Frame(self.notebook)
        # self.notebook.add(self.guest_frame, text="访客模式")
        # self._create_guest_tab()
        
        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="取消", 
                  command=self._cancel).pack(side=tk.RIGHT, padx=(10, 0))
    
    def _create_login_tab(self):
        """创建登录选项卡"""
        # 预设用户说明
        info_frame = ttk.LabelFrame(self.login_frame, text="预设用户账号")
        info_frame.pack(fill=tk.X, pady=(20, 15))

        preset_info = """快速登录预设账号:
• - 体验用户 (1次邮箱生成，不限验证码)
• - 月度会员 (4次邮箱生成，不限验证码)
• - VIP会员 (不限邮箱生成，不限验证码)"""

        info_label = ttk.Label(info_frame, text=preset_info,
                              style='Small.TLabel', justify=tk.LEFT)
        info_label.pack(padx=15, pady=10, anchor='w')

        # 快速选择按钮
        quick_frame = ttk.Frame(info_frame)
        quick_frame.pack(fill=tk.X, padx=15, pady=(0, 10))

        # quick_buttons = [
        #     ("体验123", "体验用户"),
        #     ("basic_user", "月度会员"),
        #     # ("vip_user", "VIP会员")
        # ]

        # for username, desc in quick_buttons:
        #     btn = ttk.Button(quick_frame, text=f"{desc}",
        #                    command=lambda u=username: self._quick_login(u),
        #                    style='Accent.TButton')
        #     btn.pack(side=tk.LEFT, padx=(0, 10))

        # 用户名输入 - 增加间距
        ttk.Label(self.login_frame, text="或手动输入用户名:", style='TLabel').pack(anchor='w', pady=(15, 8))
        self.login_username_entry = ttk.Entry(self.login_frame, width=40)
        self.login_username_entry.pack(fill=tk.X, pady=(0, 20))  # 15 → 20
        
        # 登录按钮 - 增加间距
        login_button = ttk.Button(self.login_frame, text="登录",
                                 command=self._login, style='TButton')
        login_button.pack(pady=15)  # 10 → 15
        
        # 用户列表 - 增加间距
        ttk.Label(self.login_frame, text="现有用户:", style='Subtitle.TLabel').pack(anchor='w', pady=(25, 12))  # (20,10) → (25,12)
        
        # 创建用户列表框架
        list_frame = ttk.Frame(self.login_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 用户列表 - 增加高度显示更多用户
        self.user_listbox = tk.Listbox(list_frame, height=10)  # 6 → 10
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.user_listbox.yview)
        self.user_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.user_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.user_listbox.bind('<Double-Button-1>', self._on_user_select)
        
        # 刷新用户列表
        # self._refresh_user_list()

    def _quick_login(self, username):
        """快速登录预设用户"""
        self.login_username_entry.delete(0, tk.END)
        self.login_username_entry.insert(0, username)
        self._login()

    # def _create_register_tab(self):
    #     """创建注册选项卡 - 暂时注释掉"""
    #     # 用户名输入 - 增加间距
    #     ttk.Label(self.register_frame, text="用户名:", style='TLabel').pack(anchor='w', pady=(25, 8))  # (20,5) → (25,8)
    #     self.register_username_entry = ttk.Entry(self.register_frame, width=40)
    #     self.register_username_entry.pack(fill=tk.X, pady=(0, 20))  # 15 → 20

    #     # 用户角色选择 - 使用副标题字体，增加间距
    #     ttk.Label(self.register_frame, text="用户角色:", style='Subtitle.TLabel').pack(anchor='w', pady=(0, 10))  # 使用副标题字体(20px)，间距8→10
    #     self.role_var = tk.StringVar(value=UserRole.BASIC.value)
    #     role_frame = ttk.Frame(self.register_frame)
    #     role_frame.pack(fill=tk.X, pady=(0, 20))  # 15 → 20
    #
    #     roles = [
    #         (UserRole.BASIC, "基础用户 (每日5个邮箱)"),
    #         (UserRole.PREMIUM, "高级用户 (每日20个邮箱)"),
    #         (UserRole.VIP, "VIP用户 (每日100个邮箱)")
    #     ]

    #     for role, description in roles:
    #         # 使用大字体样式，让文字更清晰易读
    #         radio_btn = ttk.Radiobutton(role_frame, text=description,
    #                                    variable=self.role_var, value=role.value,
    #                                    style='Large.TRadiobutton')  # 使用大字体样式 (20px)
    #         radio_btn.pack(anchor='w', pady=6)  # 进一步增加间距 4 → 6
    #
    #     # 备注输入
    #     ttk.Label(self.register_frame, text="备注 (可选):", style='TLabel').pack(anchor='w', pady=(10, 5))
    #     self.notes_entry = ttk.Entry(self.register_frame, width=40)
    #     self.notes_entry.pack(fill=tk.X, pady=(0, 15))
    #
    #     # 注册按钮
    #     register_button = ttk.Button(self.register_frame, text="注册",
    #                                command=self._register, style='Success.TButton')
    #     register_button.pack(pady=10)
    #
    #     # 权限说明
    #     info_text = ("权限说明:\n"
    #                 "• 基础用户: 每日5个邮箱，10次验证码\n"
    #                 "• 高级用户: 每日20个邮箱，50次验证码\n"
    #                 "• VIP用户: 每日100个邮箱，200次验证码\n"
    #                 "• 管理员权限需要联系系统管理员")
    #
    #     info_label = ttk.Label(self.register_frame, text=info_text,
    #                           style='Small.TLabel', justify=tk.LEFT)
    #     info_label.pack(anchor='w', pady=(20, 0))
    
    # def _create_guest_tab(self):
    #     """创建访客模式选项卡"""
    #     # 访客模式说明
    #     guest_info = ("访客模式限制:\n\n"
    #                  "• 每日只能注册 1 个邮箱\n"
    #                  "• 每日最多获取 3 次验证码\n"
    #                  "• 操作间隔限制 60 秒\n"
    #                  "• 不能使用高级功能\n\n"
    #                  "如需更多功能，请注册用户账号。")
    #
    #     ttk.Label(self.guest_frame, text="访客模式",
    #              style='Subtitle.TLabel').pack(pady=(25, 25))  # (20,20) → (25,25)
    #
    #     ttk.Label(self.guest_frame, text=guest_info,
    #              style='TLabel', justify=tk.LEFT).pack(pady=(0, 25))  # 20 → 25
    #
    #     # 访客登录按钮
    #     guest_button = ttk.Button(self.guest_frame, text="以访客身份继续",
    #                              command=self._guest_login, style='Secondary.TButton')
    #     guest_button.pack(pady=10)
    
    def _refresh_user_list(self):
        """刷新用户列表"""
        self.user_listbox.delete(0, tk.END)
        
        for user_info in self.permission_manager.users.values():
            if user_info.is_active:
                role_desc = {
                    UserRole.GUEST: "访客",
                    UserRole.BASIC: "基础",
                    UserRole.PREMIUM: "高级",
                    UserRole.VIP: "VIP",
                    UserRole.ADMIN: "管理员"
                }.get(user_info.role, "未知")
                
                display_text = f"{user_info.username} ({role_desc})"
                self.user_listbox.insert(tk.END, display_text)
    
    def _on_user_select(self, event):
        """用户列表双击事件"""
        selection = self.user_listbox.curselection()
        if selection:
            index = selection[0]
            users = [u for u in self.permission_manager.users.values() if u.is_active]
            if index < len(users):
                user = users[index]
                self.login_username_entry.delete(0, tk.END)
                self.login_username_entry.insert(0, user.username)
    
    def _login(self):
        """用户登录"""
        username = self.login_username_entry.get().strip()
        if not username:
            messagebox.showerror("错误", "请输入用户名")
            return
        
        user_info = self.permission_manager.login_user(username)
        if user_info:
            self.user_info = user_info
            self.result = "login"
            messagebox.showinfo("成功", f"欢迎回来，{username}！")
            self._close_dialog()
        else:
            messagebox.showerror("错误", "用户不存在或已被禁用")
    
    # def _register(self):
    #     """用户注册 - 暂时注释掉"""
    #     username = self.register_username_entry.get().strip()
    #     if not username:
    #         messagebox.showerror("错误", "请输入用户名")
    #         return
    #
    #     if len(username) < 2:
    #         messagebox.showerror("错误", "用户名至少需要2个字符")
    #         return
    #
    #     role = UserRole(self.role_var.get())
    #     notes = self.notes_entry.get().strip()
    #
    #     try:
    #         user_id = self.permission_manager.create_user(username, role, notes)
    #         user_info = self.permission_manager.login_user(username)
    #
    #         self.user_info = user_info
    #         self.result = "register"
    #
    #         messagebox.showinfo("成功", f"用户 {username} 注册成功！")
    #         self._refresh_user_list()
    #         self._close_dialog()
    #
    #     except ValueError as e:
    #         messagebox.showerror("错误", str(e))
    
    # def _guest_login(self):
    #     """访客登录"""
    #     # 创建或获取访客用户
    #     guest_username = "guest_user"
    #
    #     # 尝试登录现有访客用户
    #     user_info = self.permission_manager.login_user(guest_username)
    #
    #     if not user_info:
    #         # 创建新的访客用户
    #         try:
    #             user_id = self.permission_manager.create_user(guest_username, UserRole.GUEST, "访客用户")
    #             user_info = self.permission_manager.login_user(guest_username)
    #         except ValueError:
    #             # 访客用户已存在但未激活，直接获取
    #             for user in self.permission_manager.users.values():
    #                 if user.username == guest_username:
    #                     user.is_active = True
    #                     user_info = self.permission_manager.login_user(guest_username)
    #                     break
    #
    #     if user_info:
    #         self.user_info = user_info
    #         self.result = "guest"
    #         messagebox.showinfo("访客模式", "已进入访客模式，功能受限")
    #         self._close_dialog()
    #     else:
    #         messagebox.showerror("错误", "无法进入访客模式")
    
    def _cancel(self):
        """取消操作"""
        self.result = "cancel"
        self._close_dialog()
    
    def _close_dialog(self):
        """关闭对话框"""
        if self.parent:
            self.dialog.grab_release()
        self.dialog.destroy()
    
    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result, self.user_info


def main():
    """测试用户认证对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    auth_dialog = UserAuthDialog()
    result, user_info = auth_dialog.show()
    
    if result and user_info:
        print(f"认证结果: {result}")
        print(f"用户信息: {user_info.username} ({user_info.role.value})")
    else:
        print("用户取消了认证")
    
    root.destroy()


if __name__ == "__main__":
    main()
