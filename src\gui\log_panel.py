"""
日志显示面板
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
from src.gui.theme_manager import theme_manager
from src.gui.custom_widgets import ModernButton, StyledTreeview
from src.gui.layout_manager import LayoutManager


class LogPanel:
    """日志显示面板"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self._is_compact_mode = False

        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面组件"""
        self.frame = ttk.Frame(self.parent)
        self.frame.configure(style='TFrame')
        
        # 创建标题区域
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = ttk.Label(title_frame, text="日志查看", 
                               style='Title.TLabel')
        title_label.pack(anchor='center')
        
        # 创建控制面板
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill=tk.X, padx=20, pady=(0, 15))
        
        # 创建左侧控制区域
        left_control = ttk.Frame(control_frame)
        left_control.pack(side=tk.LEFT)
        
        # 日志级别过滤
        filter_frame = ttk.Frame(left_control)
        filter_frame.pack(side=tk.LEFT, padx=(0, 15))
        
        ttk.Label(filter_frame, text="过滤级别:").pack(side=tk.LEFT)
        
        self.level_var = tk.StringVar(value="全部")
        level_combo = ttk.Combobox(filter_frame, textvariable=self.level_var, 
                                  values=["全部", "INFO", "SUCCESS", "WARNING", "ERROR"], 
                                  width=10, state="readonly")
        level_combo.pack(side=tk.LEFT, padx=5)
        level_combo.bind("<<ComboboxSelected>>", self._on_level_change)
        
        # 创建按钮组
        refresh_btn = ModernButton(control_frame, text="刷新", 
                                 command=self.refresh_logs)
        
        clear_btn = ModernButton(control_frame, text="清除日志", 
                               style='Secondary.TButton',
                               command=self._on_clear_click)
        
        export_btn = ModernButton(control_frame, text="导出日志", 
                                style='Success.TButton',
                                command=self._on_export_click)
        
        # 使用布局管理器创建按钮组
        buttons = [refresh_btn, clear_btn, export_btn]
        button_group = LayoutManager.create_button_group(control_frame, buttons)
        button_group.pack(side=tk.RIGHT)
        
        # 创建日志显示区域
        log_frame = LayoutManager.create_section_frame(self.frame, "日志内容")
        
        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=20, 
                               font=(theme_manager.monospace_font, 10),
                               background=theme_manager.colors['white'],
                               borderwidth=1,
                               relief='solid')
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, 
                                     command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        # 布局
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)
        
        # 设置文本框为只读
        self.log_text.config(state=tk.DISABLED)
        
        # 配置标签 - 使用主题管理器中定义的颜色
        self.log_text.tag_configure("INFO", foreground=theme_manager.colors['text_primary'])
        self.log_text.tag_configure("SUCCESS", foreground=theme_manager.colors['success'])
        self.log_text.tag_configure("WARNING", foreground=theme_manager.colors['warning'])
        self.log_text.tag_configure("ERROR", foreground=theme_manager.colors['danger'])
        self.log_text.tag_configure("TIMESTAMP", foreground=theme_manager.colors['text_secondary'])
        
        # 底部状态区域
        status_frame = ttk.Frame(self.frame)
        status_frame.pack(fill=tk.X, padx=20, pady=(5, 15))
        
        self.status_label = ttk.Label(status_frame, text="日志条目: 0",
                                    style='Small.TLabel')
        self.status_label.pack(side=tk.LEFT)
        
        # 添加日志统计信息
        self.stats_label = ttk.Label(status_frame, text="",
                                   style='Small.TLabel')
        self.stats_label.pack(side=tk.RIGHT)
        
        # 初始加载日志
        self.refresh_logs()
    
    def _on_level_change(self, event):
        """日志级别变化事件"""
        self.refresh_logs()
    
    def _on_clear_click(self):
        """清除按钮点击事件"""
        result = messagebox.askyesno("确认", "确定要清除所有日志吗？")
        if result:
            self.main_window.app.log_manager.clear_logs()
            self.refresh_logs()
            self.main_window.status_bar.set_status("已清除所有日志", "INFO")
    
    def _on_export_click(self):
        """导出按钮点击事件"""
        try:
            # 获取保存路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"augment_auto_logs_{timestamp}.txt"
            
            file_path = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                initialfile=default_filename
            )
            
            if not file_path:
                return  # 用户取消了操作
            
            # 导出日志
            exported_path = self.main_window.app.log_manager.export_logs(file_path)
            
            self.main_window.app.log_manager.add_log(f"日志已导出到: {exported_path}", "SUCCESS")
            self.main_window.status_bar.set_status(f"日志已导出到: {exported_path}", "SUCCESS")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出日志失败: {str(e)}")
    
    def refresh_logs(self):
        """刷新日志显示"""
        # 获取过滤级别
        level_filter = self.level_var.get()
        if level_filter == "全部":
            level_filter = None
        
        # 获取日志
        logs = self.main_window.app.log_manager.get_logs(level_filter)
        
        # 清空文本框
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        
        # 添加日志条目
        for log in logs:
            # 格式化时间戳
            timestamp = log.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            
            # 插入时间戳
            self.log_text.insert(tk.END, f"[{timestamp}] ", "TIMESTAMP")
            
            # 插入级别和消息
            self.log_text.insert(tk.END, f"[{log.level}] {log.message}\n", log.level)
        
        # 滚动到底部
        self.log_text.see(tk.END)
        
        # 恢复只读状态
        self.log_text.config(state=tk.DISABLED)
        
        # 更新状态
        self.status_label.config(text=f"日志条目: {len(logs)}")
        
        # 更新统计信息
        self._update_stats()
    
    def _update_stats(self):
        """更新日志统计信息"""
        stats = self.main_window.app.log_manager.get_log_stats()
        
        # 格式化统计信息
        stats_text = f"INFO: {stats.get('INFO', 0)} | "
        stats_text += f"SUCCESS: {stats.get('SUCCESS', 0)} | "
        stats_text += f"WARNING: {stats.get('WARNING', 0)} | "
        stats_text += f"ERROR: {stats.get('ERROR', 0)}"
        
        self.stats_label.config(text=stats_text)

    def set_compact_mode(self, is_compact):
        """设置紧凑模式"""
        if self._is_compact_mode == is_compact:
            return

        self._is_compact_mode = is_compact

        if is_compact:
            self._apply_compact_layout()
        else:
            self._apply_normal_layout()

    def _apply_compact_layout(self):
        """应用紧凑布局"""
        # 减少内边距
        for child in self.frame.winfo_children():
            if isinstance(child, ttk.Frame):
                child.pack_configure(padx=10, pady=5)

        # 调整日志树形视图高度
        if hasattr(self, 'log_tree'):
            self.log_tree.configure(height=10)

    def _apply_normal_layout(self):
        """应用正常布局"""
        # 恢复内边距
        for child in self.frame.winfo_children():
            if isinstance(child, ttk.Frame):
                child.pack_configure(padx=20, pady=10)

        # 恢复日志树形视图高度
        if hasattr(self, 'log_tree'):
            self.log_tree.configure(height=15)