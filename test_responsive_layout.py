#!/usr/bin/env python3
"""
响应式布局测试脚本
用于测试GUI界面在不同窗口大小下的显示效果
"""
import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from src.app import Application


class ResponsiveLayoutTester:
    """响应式布局测试器"""
    
    def __init__(self):
        self.app = None
        self.test_window = None
        self.current_size = "normal"
        
    def create_test_window(self):
        """创建测试窗口"""
        self.test_window = tk.Toplevel()
        self.test_window.title("响应式布局测试控制面板")
        self.test_window.geometry("400x300")
        self.test_window.resizable(False, False)
        
        # 创建控制面板
        control_frame = ttk.Frame(self.test_window)
        control_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(control_frame, text="响应式布局测试", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 当前状态显示
        self.status_label = ttk.Label(control_frame, text="当前模式: 正常模式")
        self.status_label.pack(pady=(0, 10))
        
        # 测试按钮组
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 预设尺寸测试按钮
        sizes = [
            ("大屏幕 (1200x800)", 1200, 800),
            ("正常尺寸 (900x700)", 900, 700),
            ("紧凑模式 (750x600)", 750, 600),
            ("最小尺寸 (700x500)", 700, 500),
        ]
        
        for text, width, height in sizes:
            btn = ttk.Button(button_frame, text=text,
                           command=lambda w=width, h=height: self.set_window_size(w, h))
            btn.pack(fill=tk.X, pady=2)
        
        # 分隔线
        separator = ttk.Separator(control_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=20)
        
        # 手动调整区域
        manual_frame = ttk.LabelFrame(control_frame, text="手动调整")
        manual_frame.pack(fill=tk.X, pady=10)
        
        # 宽度调整
        width_frame = ttk.Frame(manual_frame)
        width_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(width_frame, text="宽度:").pack(side=tk.LEFT)
        self.width_var = tk.StringVar(value="900")
        width_entry = ttk.Entry(width_frame, textvariable=self.width_var, width=10)
        width_entry.pack(side=tk.LEFT, padx=5)
        
        # 高度调整
        height_frame = ttk.Frame(manual_frame)
        height_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(height_frame, text="高度:").pack(side=tk.LEFT)
        self.height_var = tk.StringVar(value="700")
        height_entry = ttk.Entry(height_frame, textvariable=self.height_var, width=10)
        height_entry.pack(side=tk.LEFT, padx=5)
        
        # 应用按钮
        apply_btn = ttk.Button(manual_frame, text="应用尺寸", 
                              command=self.apply_manual_size)
        apply_btn.pack(pady=10)
        
        # 测试信息
        info_frame = ttk.LabelFrame(control_frame, text="测试信息")
        info_frame.pack(fill=tk.X, pady=10)
        
        info_text = ("• 紧凑模式阈值: 800px\n"
                    "• 最小窗口尺寸: 700x500\n"
                    "• 测试重点: 按钮布局、表格显示、内边距调整")
        
        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
        info_label.pack(padx=10, pady=10, anchor=tk.W)
        
    def set_window_size(self, width, height):
        """设置主窗口大小"""
        if self.app and self.app.main_window:
            self.app.main_window.root.geometry(f"{width}x{height}")
            
            # 更新状态显示
            if width < 800:
                mode = "紧凑模式"
            else:
                mode = "正常模式"
            
            self.status_label.config(text=f"当前模式: {mode} ({width}x{height})")
            
            # 强制触发resize事件
            self.app.main_window.root.event_generate('<Configure>')
    
    def apply_manual_size(self):
        """应用手动设置的尺寸"""
        try:
            width = int(self.width_var.get())
            height = int(self.height_var.get())
            
            # 限制最小尺寸
            width = max(width, 700)
            height = max(height, 500)
            
            self.set_window_size(width, height)
            
        except ValueError:
            tk.messagebox.showerror("错误", "请输入有效的数字")
    
    def run_test(self):
        """运行测试"""
        try:
            # 创建应用实例
            self.app = Application()
            
            # 创建测试控制窗口
            self.create_test_window()
            
            # 设置测试窗口关闭事件
            self.test_window.protocol("WM_DELETE_WINDOW", self.on_test_window_close)
            
            # 运行应用
            self.app.run()
            
        except Exception as e:
            print(f"测试运行失败: {e}")
            import traceback
            traceback.print_exc()
    
    def on_test_window_close(self):
        """测试窗口关闭事件"""
        self.test_window.destroy()


def main():
    """主函数"""
    print("启动响应式布局测试...")
    print("=" * 50)
    print("测试说明:")
    print("1. 主窗口将正常启动")
    print("2. 测试控制面板将同时打开")
    print("3. 使用控制面板调整主窗口大小")
    print("4. 观察界面组件的响应式变化")
    print("5. 重点测试紧凑模式(宽度<800px)的效果")
    print("=" * 50)
    
    tester = ResponsiveLayoutTester()
    tester.run_test()


if __name__ == "__main__":
    main()
