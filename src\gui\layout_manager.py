"""
布局管理器
"""
import tkinter as tk
from tkinter import ttk
from src.gui.theme_manager import theme_manager


class LayoutManager:
    """布局管理器，负责优化界面布局"""
    
    @staticmethod
    def apply_grid_layout(container, padding=10):
        """应用网格布局，自动设置合适的内边距"""
        for child in container.winfo_children():
            child.grid_configure(padx=padding, pady=padding)
    
    @staticmethod
    def create_section_frame(parent, title=None, padding=10):
        """创建带标题的分区框架"""
        if title:
            frame = ttk.LabelFrame(parent, text=title)
        else:
            frame = ttk.Frame(parent)
        
        frame.pack(fill='both', expand=True, padx=padding, pady=padding)
        return frame
    
    @staticmethod
    def create_button_group(parent, buttons, orientation='horizontal', padding=5):
        """创建按钮组，水平或垂直排列"""
        frame = ttk.Frame(parent)
        
        if orientation == 'horizontal':
            for i, btn in enumerate(buttons):
                btn.pack(side='left', padx=(0 if i == 0 else padding))
        else:
            for i, btn in enumerate(buttons):
                btn.pack(side='top', pady=(0 if i == 0 else padding))
        
        return frame
    
    @staticmethod
    def create_form_field(parent, label_text, widget_class, widget_args=None, **kwargs):
        """创建表单字段（标签+输入控件）"""
        if widget_args is None:
            widget_args = {}
        
        # 创建框架
        frame = ttk.Frame(parent)
        
        # 创建标签
        label = ttk.Label(frame, text=label_text)
        label.pack(side='top', anchor='w', pady=(0, 2))
        
        # 创建控件
        widget = widget_class(frame, **widget_args)
        widget.pack(side='top', fill='x', expand=True)
        
        # 应用额外参数
        for key, value in kwargs.items():
            frame.pack_configure(**{key: value})
        
        return frame, widget
    
    @staticmethod
    def create_scrollable_frame(parent, **kwargs):
        """创建可滚动的框架"""
        # 创建容器框架
        container = ttk.Frame(parent)
        
        # 创建画布
        canvas = tk.Canvas(container, highlightthickness=0, **kwargs)
        
        # 创建滚动条
        scrollbar = ttk.Scrollbar(container, orient='vertical', command=canvas.yview)
        
        # 创建可滚动的框架
        scrollable_frame = ttk.Frame(canvas)
        scrollable_frame.bind(
            '<Configure>',
            lambda e: canvas.configure(scrollregion=canvas.bbox('all'))
        )
        
        # 在画布上创建窗口
        canvas.create_window((0, 0), window=scrollable_frame, anchor='nw')
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        canvas.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1 * (event.delta / 120)), 'units')
        
        canvas.bind_all('<MouseWheel>', _on_mousewheel)
        
        # 返回容器和可滚动框架
        return container, scrollable_frame
    
    @staticmethod
    def create_status_message(parent, message='', message_type='info'):
        """创建状态消息显示"""
        # 根据消息类型选择颜色
        colors = {
            'info': theme_manager.colors['info'],
            'success': theme_manager.colors['success'],
            'warning': theme_manager.colors['warning'],
            'error': theme_manager.colors['danger']
        }
        
        bg_color = colors.get(message_type, theme_manager.colors['info'])
        
        # 创建消息框架
        frame = tk.Frame(parent, bg=bg_color, padx=10, pady=5)
        
        # 创建消息标签
        label = ttk.Label(frame, text=message, foreground=theme_manager.colors['white'],
                         background=bg_color)
        label.pack(fill='x')
        
        return frame, label
    
    @staticmethod
    def create_card(parent, title=None, content=None, **kwargs):
        """创建卡片式布局"""
        # 创建卡片框架
        card = ttk.Frame(parent, style='Card.TFrame')
        
        # 创建标题（如果有）
        if title:
            title_label = ttk.Label(card, text=title, style='Subtitle.TLabel')
            title_label.pack(anchor='w', padx=10, pady=10)
            
            # 添加分隔线
            separator = ttk.Separator(card, orient='horizontal')
            separator.pack(fill='x', padx=5)
        
        # 创建内容（如果有）
        if content:
            content_frame = ttk.Frame(card)
            content_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            if isinstance(content, str):
                # 如果内容是字符串，创建标签
                content_label = ttk.Label(content_frame, text=content)
                content_label.pack(anchor='w')
            else:
                # 如果内容是部件，添加到内容框架
                content.pack(in_=content_frame, fill='both', expand=True)
        
        # 应用额外参数
        for key, value in kwargs.items():
            card.pack_configure(**{key: value})
        
        return card

    @staticmethod
    def create_responsive_frame(parent, normal_padding=10, compact_padding=5):
        """创建响应式框架"""
        frame = ttk.Frame(parent)
        frame._normal_padding = normal_padding
        frame._compact_padding = compact_padding
        frame._is_compact = False

        # 添加响应式方法
        def set_compact_mode(is_compact):
            if frame._is_compact == is_compact:
                return
            frame._is_compact = is_compact
            padding = frame._compact_padding if is_compact else frame._normal_padding
            frame.pack_configure(padx=padding, pady=padding)

        frame.set_compact_mode = set_compact_mode
        return frame

    @staticmethod
    def create_responsive_button_group(parent, buttons, normal_spacing=5, compact_spacing=2):
        """创建响应式按钮组"""
        frame = ttk.Frame(parent)
        frame._buttons = buttons
        frame._normal_spacing = normal_spacing
        frame._compact_spacing = compact_spacing
        frame._is_compact = False

        # 初始布局
        for i, btn in enumerate(buttons):
            btn.pack(side='left', padx=(0 if i == 0 else normal_spacing))

        def set_compact_mode(is_compact):
            if frame._is_compact == is_compact:
                return
            frame._is_compact = is_compact
            spacing = frame._compact_spacing if is_compact else frame._normal_spacing

            # 重新布局按钮
            for i, btn in enumerate(frame._buttons):
                btn.pack_configure(padx=(0 if i == 0 else spacing))

        frame.set_compact_mode = set_compact_mode
        return frame

    @staticmethod
    def create_responsive_treeview(parent, columns, normal_height=15, compact_height=10):
        """创建响应式树形视图"""
        from src.gui.custom_widgets import StyledTreeview

        tree = StyledTreeview(parent, columns=columns, show="headings", height=normal_height)
        tree._normal_height = normal_height
        tree._compact_height = compact_height
        tree._is_compact = False

        def set_compact_mode(is_compact):
            if tree._is_compact == is_compact:
                return
            tree._is_compact = is_compact
            height = tree._compact_height if is_compact else tree._normal_height
            tree.configure(height=height)

        tree.set_compact_mode = set_compact_mode
        return tree

    @staticmethod
    def apply_responsive_layout(widget, is_compact):
        """对支持响应式的组件应用布局"""
        if hasattr(widget, 'set_compact_mode'):
            widget.set_compact_mode(is_compact)

        # 递归应用到子组件
        try:
            for child in widget.winfo_children():
                LayoutManager.apply_responsive_layout(child, is_compact)
        except:
            pass

    @staticmethod
    def create_adaptive_grid(parent, items, max_columns=3, min_columns=1):
        """创建自适应网格布局"""
        frame = ttk.Frame(parent)
        frame._items = items
        frame._max_columns = max_columns
        frame._min_columns = min_columns
        frame._current_columns = max_columns

        def _layout_items(columns):
            # 清除现有布局
            for item in frame._items:
                item.grid_forget()

            # 重新布局
            for i, item in enumerate(frame._items):
                row = i // columns
                col = i % columns
                item.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

            # 配置列权重
            for col in range(columns):
                frame.grid_columnconfigure(col, weight=1)

        def set_compact_mode(is_compact):
            columns = frame._min_columns if is_compact else frame._max_columns
            if columns != frame._current_columns:
                frame._current_columns = columns
                _layout_items(columns)

        # 初始布局
        _layout_items(frame._current_columns)

        frame.set_compact_mode = set_compact_mode
        return frame