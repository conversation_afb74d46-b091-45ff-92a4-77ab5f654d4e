"""
用户权限管理系统
支持不同用户的不同注册限制策略
"""
import json
import hashlib
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import os


class UserRole(Enum):
    """用户角色枚举"""
    GUEST = "guest"           # 访客用户
    TRIAL = "trial"           # 体验用户 (体验123)
    BASIC = "basic"           # 月度会员 (basic_user)
    PREMIUM = "premium"       # 高级用户
    VIP = "vip"              # VIP会员 (vip_user)
    ADMIN = "admin"          # 管理员


class LimitType(Enum):
    """限制类型枚举"""
    DAILY = "daily"          # 每日限制
    WEEKLY = "weekly"        # 每周限制
    MONTHLY = "monthly"      # 每月限制
    TOTAL = "total"          # 总数限制
    UNLIMITED = "unlimited"  # 无限制


@dataclass
class UserPermission:
    """用户权限配置"""
    role: UserRole
    email_limit: int                    # 邮箱注册数量限制
    limit_type: LimitType              # 限制类型
    verification_limit: int            # 验证码获取限制
    rate_limit_seconds: int            # 操作间隔限制（秒）
    allowed_domains: List[str]         # 允许的邮箱域名
    blocked_domains: List[str]         # 禁止的邮箱域名
    max_concurrent_sessions: int       # 最大并发会话数
    features: List[str]                # 可用功能列表


@dataclass
class UserInfo:
    """用户信息"""
    user_id: str
    username: str
    role: UserRole
    created_at: datetime
    last_login: datetime
    email_count: int                   # 已注册邮箱数量
    verification_count: int            # 已获取验证码数量
    last_operation_time: datetime      # 最后操作时间
    is_active: bool                    # 是否激活
    notes: str                         # 备注信息


class UserPermissionManager:
    """用户权限管理器"""
    
    def __init__(self, data_dir: str = "data/users"):
        """初始化权限管理器"""
        self.data_dir = data_dir
        self.users_file = os.path.join(data_dir, "users.json")
        self.permissions_file = os.path.join(data_dir, "permissions.json")
        self.usage_log_file = os.path.join(data_dir, "usage_log.json")
        
        # 确保数据目录存在
        os.makedirs(data_dir, exist_ok=True)
        
        # 初始化默认权限配置
        self.default_permissions = self._init_default_permissions()

        # 加载数据
        self.users = self._load_users()
        self.permissions = self._load_permissions()
        self.current_user = None

        # 初始化预设用户
        self._init_preset_users()
    
    def _init_default_permissions(self) -> Dict[UserRole, UserPermission]:
        """初始化默认权限配置"""
        return {
            UserRole.GUEST: UserPermission(
                role=UserRole.GUEST,
                email_limit=1,
                limit_type=LimitType.DAILY,
                verification_limit=3,
                rate_limit_seconds=60,
                allowed_domains=[],
                blocked_domains=["tempmail.com", "10minutemail.com"],
                max_concurrent_sessions=1,
                features=["basic_email_generation"]
            ),
            UserRole.TRIAL: UserPermission(
                role=UserRole.TRIAL,
                email_limit=1,                    # 体验用户：只能生成1次邮箱
                limit_type=LimitType.DAILY,
                verification_limit=-1,            # 不限获取验证码次数
                rate_limit_seconds=10,
                allowed_domains=[],
                blocked_domains=[],
                max_concurrent_sessions=1,
                features=["basic_email_generation", "verification_code"]
            ),
            UserRole.BASIC: UserPermission(
                role=UserRole.BASIC,
                email_limit=4,                    # 月度会员：总共可以生成4次邮箱
                limit_type=LimitType.TOTAL,       # 修改为总计限制
                verification_limit=-1,            # 不限获取验证码次数
                rate_limit_seconds=10,
                allowed_domains=[],
                blocked_domains=[],
                max_concurrent_sessions=2,
                features=["basic_email_generation", "verification_code"]
            ),
            UserRole.PREMIUM: UserPermission(
                role=UserRole.PREMIUM,
                email_limit=20,
                limit_type=LimitType.DAILY,
                verification_limit=50,
                rate_limit_seconds=10,
                allowed_domains=[],
                blocked_domains=[],
                max_concurrent_sessions=3,
                features=["basic_email_generation", "verification_code", "batch_generation"]
            ),
            UserRole.VIP: UserPermission(
                role=UserRole.VIP,
                email_limit=-1,                   # VIP会员：不限生成邮箱次数
                limit_type=LimitType.DAILY,
                verification_limit=-1,            # 不限获取验证码次数
                rate_limit_seconds=5,
                allowed_domains=[],
                blocked_domains=[],
                max_concurrent_sessions=5,
                features=["basic_email_generation", "verification_code", "batch_generation", "custom_domains"]
            ),
            UserRole.ADMIN: UserPermission(
                role=UserRole.ADMIN,
                email_limit=-1,  # -1 表示无限制
                limit_type=LimitType.UNLIMITED,
                verification_limit=-1,
                rate_limit_seconds=0,
                allowed_domains=[],
                blocked_domains=[],
                max_concurrent_sessions=10,
                features=["all"]
            )
        }

    def _init_preset_users(self):
        """初始化预设用户"""
        preset_users = [
            {
                "username": "trial",
                "role": UserRole.TRIAL,
                "notes": "体验用户 - 1次邮箱生成，不限验证码"
            },
            {
                "username": "monthly_user",
                "role": UserRole.BASIC,
                "notes": "月度会员 - 总共4次邮箱生成，不限验证码"
            },
            {
                "username": "super_user",
                "role": UserRole.VIP,
                "notes": "VIP会员 - 不限邮箱生成，不限验证码"
            }
        ]

        for preset in preset_users:
            username = preset["username"]
            # 检查用户是否已存在（通过用户名查找）
            user_exists = any(user.username == username for user in self.users.values())

            if not user_exists:
                try:
                    self.create_user(username, preset["role"], preset["notes"])
                    print(f"创建预设用户: {username} ({preset['role'].value})")
                except ValueError as e:
                    # 用户可能已存在，忽略错误
                    print(f"预设用户 {username} 已存在，跳过创建")
                    pass
            else:
                print(f"预设用户 {username} 已存在，跳过创建")

    def _load_users(self) -> Dict[str, UserInfo]:
        """加载用户数据"""
        if not os.path.exists(self.users_file):
            return {}
        
        try:
            with open(self.users_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                users = {}
                for user_id, user_data in data.items():
                    user_data['role'] = UserRole(user_data['role'])
                    user_data['created_at'] = datetime.fromisoformat(user_data['created_at'])
                    user_data['last_login'] = datetime.fromisoformat(user_data['last_login'])
                    user_data['last_operation_time'] = datetime.fromisoformat(user_data['last_operation_time'])
                    users[user_id] = UserInfo(**user_data)
                return users
        except Exception as e:
            print(f"加载用户数据失败: {e}")
            return {}
    
    def _save_users(self):
        """保存用户数据"""
        try:
            data = {}
            for user_id, user_info in self.users.items():
                user_dict = asdict(user_info)
                user_dict['role'] = user_info.role.value
                user_dict['created_at'] = user_info.created_at.isoformat()
                user_dict['last_login'] = user_info.last_login.isoformat()
                user_dict['last_operation_time'] = user_info.last_operation_time.isoformat()
                data[user_id] = user_dict
                # 调试信息
                if user_info.username == "体验123":
                    print(f"准备保存用户{user_info.username}: email_count={user_info.email_count}")

            print(f"保存到文件: {self.users_file}")
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"用户数据保存成功，当前用户email_count: {self.current_user.email_count if self.current_user else 'None'}")
        except Exception as e:
            print(f"保存用户数据失败: {e}")
    
    def _load_permissions(self) -> Dict[UserRole, UserPermission]:
        """加载权限配置"""
        if not os.path.exists(self.permissions_file):
            # 首次运行时保存默认权限配置
            permissions = self.default_permissions.copy()
            self.permissions = permissions  # 临时设置以便保存
            self._save_permissions()
            return permissions
        
        try:
            with open(self.permissions_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                permissions = {}
                for role_str, perm_data in data.items():
                    role = UserRole(role_str)
                    perm_data['role'] = role
                    perm_data['limit_type'] = LimitType(perm_data['limit_type'])
                    permissions[role] = UserPermission(**perm_data)
                return permissions
        except Exception as e:
            print(f"加载权限配置失败: {e}")
            return self.default_permissions.copy()
    
    def _save_permissions(self):
        """保存权限配置"""
        try:
            data = {}
            for role, permission in self.permissions.items():
                perm_dict = asdict(permission)
                perm_dict['role'] = role.value
                perm_dict['limit_type'] = permission.limit_type.value
                data[role.value] = perm_dict
            
            with open(self.permissions_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存权限配置失败: {e}")
    
    def create_user(self, username: str, role: UserRole = UserRole.BASIC, notes: str = "") -> str:
        """创建新用户"""
        user_id = self._generate_user_id(username)
        
        if user_id in self.users:
            raise ValueError(f"用户 {username} 已存在")
        
        user_info = UserInfo(
            user_id=user_id,
            username=username,
            role=role,
            created_at=datetime.now(),
            last_login=datetime.now(),
            email_count=0,
            verification_count=0,
            last_operation_time=datetime.now(),
            is_active=True,
            notes=notes
        )
        
        self.users[user_id] = user_info
        self._save_users()
        
        return user_id
    
    def _generate_user_id(self, username: str) -> str:
        """生成用户ID"""
        timestamp = str(int(time.time()))
        hash_input = f"{username}_{timestamp}"
        return hashlib.md5(hash_input.encode()).hexdigest()[:12]
    
    def login_user(self, username: str) -> Optional[UserInfo]:
        """用户登录"""
        for user_info in self.users.values():
            if user_info.username == username and user_info.is_active:
                user_info.last_login = datetime.now()
                self.current_user = user_info
                self._save_users()
                return user_info
        return None
    
    def logout_user(self):
        """用户登出"""
        self.current_user = None

    def check_email_limit(self, user_id: str = None) -> Tuple[bool, str, int, int]:
        """
        检查邮箱注册限制
        返回: (是否允许, 原因, 已使用数量, 限制数量)
        """
        user = self.current_user if user_id is None else self.users.get(user_id)
        if not user:
            return False, "用户未登录", 0, 0

        permission = self.permissions.get(user.role)
        if not permission:
            return False, "用户权限配置不存在", 0, 0

        # 无限制用户
        if permission.email_limit == -1:
            return True, "无限制", user.email_count, -1

        # 获取当前周期内的使用量
        current_usage = self._get_current_usage(user, 'email', permission.limit_type)

        if current_usage >= permission.email_limit:
            limit_desc = self._get_limit_description(permission.limit_type)
            return False, f"已达到{limit_desc}邮箱注册限制", current_usage, permission.email_limit

        return True, "允许注册", current_usage, permission.email_limit

    def check_verification_limit(self, user_id: str = None) -> Tuple[bool, str, int, int]:
        """
        检查验证码获取限制
        返回: (是否允许, 原因, 已使用数量, 限制数量)
        """
        user = self.current_user if user_id is None else self.users.get(user_id)
        if not user:
            return False, "用户未登录", 0, 0

        permission = self.permissions.get(user.role)
        if not permission:
            return False, "用户权限配置不存在", 0, 0

        # 无限制用户
        if permission.verification_limit == -1:
            return True, "无限制", user.verification_count, -1

        # 获取当前周期内的使用量
        current_usage = self._get_current_usage(user, 'verification', permission.limit_type)

        if current_usage >= permission.verification_limit:
            limit_desc = self._get_limit_description(permission.limit_type)
            return False, f"已达到{limit_desc}验证码获取限制", current_usage, permission.verification_limit

        return True, "允许获取", current_usage, permission.verification_limit

    def check_rate_limit(self, user_id: str = None) -> Tuple[bool, str, int]:
        """
        检查操作频率限制
        返回: (是否允许, 原因, 剩余等待时间)
        """
        user = self.current_user if user_id is None else self.users.get(user_id)
        if not user:
            return False, "用户未登录", 0

        permission = self.permissions.get(user.role)
        if not permission or permission.rate_limit_seconds == 0:
            return True, "无频率限制", 0

        time_since_last = (datetime.now() - user.last_operation_time).total_seconds()

        if time_since_last < permission.rate_limit_seconds:
            wait_time = int(permission.rate_limit_seconds - time_since_last)
            return False, f"操作过于频繁，请等待{wait_time}秒", wait_time

        return True, "允许操作", 0

    def record_email_generation(self, user_id: str = None) -> bool:
        """记录邮箱生成操作"""
        user = self.current_user if user_id is None else self.users.get(user_id)
        if not user:
            print("记录邮箱生成失败: 用户不存在")
            return False

        old_count = user.email_count
        user.email_count += 1
        user.last_operation_time = datetime.now()
        print(f"记录邮箱生成: 用户{user.username}, email_count从{old_count}增加到{user.email_count}")
        self._log_usage(user.user_id, 'email_generation')
        self._save_users()
        return True

    def record_verification_request(self, user_id: str = None) -> bool:
        """记录验证码请求操作"""
        user = self.current_user if user_id is None else self.users.get(user_id)
        if not user:
            return False

        user.verification_count += 1
        user.last_operation_time = datetime.now()
        self._log_usage(user.user_id, 'verification_request')
        self._save_users()
        return True

    def _get_current_usage(self, user: UserInfo, operation_type: str, limit_type: LimitType) -> int:
        """获取当前周期内的使用量"""
        if limit_type == LimitType.TOTAL:
            return user.email_count if operation_type == 'email' else user.verification_count

        # 计算周期开始时间
        now = datetime.now()
        if limit_type == LimitType.DAILY:
            period_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        elif limit_type == LimitType.WEEKLY:
            days_since_monday = now.weekday()
            period_start = (now - timedelta(days=days_since_monday)).replace(hour=0, minute=0, second=0, microsecond=0)
        elif limit_type == LimitType.MONTHLY:
            period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        else:
            return 0

        # 从使用日志中统计周期内的使用量
        return self._count_usage_in_period(user.user_id, operation_type, period_start, now)

    def _count_usage_in_period(self, user_id: str, operation_type: str, start_time: datetime, end_time: datetime) -> int:
        """统计指定时间段内的使用量"""
        try:
            if not os.path.exists(self.usage_log_file):
                return 0

            with open(self.usage_log_file, 'r', encoding='utf-8') as f:
                logs = json.load(f)

            count = 0
            for log in logs:
                if (log['user_id'] == user_id and
                    log['operation'] == operation_type and
                    start_time <= datetime.fromisoformat(log['timestamp']) <= end_time):
                    count += 1

            return count
        except Exception as e:
            print(f"统计使用量失败: {e}")
            return 0

    def _log_usage(self, user_id: str, operation: str):
        """记录使用日志"""
        try:
            log_entry = {
                'user_id': user_id,
                'operation': operation,
                'timestamp': datetime.now().isoformat()
            }

            logs = []
            if os.path.exists(self.usage_log_file):
                with open(self.usage_log_file, 'r', encoding='utf-8') as f:
                    logs = json.load(f)

            logs.append(log_entry)

            # 保留最近30天的日志
            cutoff_time = datetime.now() - timedelta(days=30)
            logs = [log for log in logs if datetime.fromisoformat(log['timestamp']) > cutoff_time]

            with open(self.usage_log_file, 'w', encoding='utf-8') as f:
                json.dump(logs, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"记录使用日志失败: {e}")

    def _get_limit_description(self, limit_type: LimitType) -> str:
        """获取限制类型描述"""
        descriptions = {
            LimitType.DAILY: "每日",
            LimitType.WEEKLY: "每周",
            LimitType.MONTHLY: "每月",
            LimitType.TOTAL: "总计",
            LimitType.UNLIMITED: "无限制"
        }
        return descriptions.get(limit_type, "未知")

    def get_user_stats(self, user_id: str = None) -> Dict:
        """获取用户统计信息"""
        user = self.current_user if user_id is None else self.users.get(user_id)
        if not user:
            return {}

        permission = self.permissions.get(user.role)
        if not permission:
            return {}

        # 获取各种限制的当前使用情况
        email_allowed, email_reason, email_used, email_limit = self.check_email_limit(user_id)
        verification_allowed, verification_reason, verification_used, verification_limit = self.check_verification_limit(user_id)
        rate_allowed, rate_reason, wait_time = self.check_rate_limit(user_id)

        return {
            'user_info': {
                'username': user.username,
                'role': user.role.value,
                'created_at': user.created_at.isoformat(),
                'last_login': user.last_login.isoformat(),
                'is_active': user.is_active
            },
            'limits': {
                'email': {
                    'allowed': email_allowed,
                    'used': email_used,
                    'limit': email_limit,
                    'limit_type': permission.limit_type.value
                },
                'verification': {
                    'allowed': verification_allowed,
                    'used': verification_used,
                    'limit': verification_limit,
                    'limit_type': permission.limit_type.value
                },
                'rate_limit': {
                    'allowed': rate_allowed,
                    'wait_time': wait_time,
                    'limit_seconds': permission.rate_limit_seconds
                }
            },
            'features': permission.features
        }
