# Tampermonkey安装与AugmentCode自动注册脚本配置指南

## 📋 目录

1. [安装Tampermonkey插件](#安装tampermonkey插件)
2. [添加自动注册脚本](#添加自动注册脚本)
3. [在Cursor中安装Augment插件](#在cursor中安装augment插件)
4. [Cursor编辑器界面优化设置](#cursor编辑器界面优化设置)
5. [脚本使用说明](#脚本使用说明)
6. [常见问题解决](#常见问题解决)

---

## 🔧 安装Tampermonkey插件

### Microsoft Edge浏览器安装步骤

**⚠️ 重要提示：***需要将浏览器的开发者模式打开，如图***

![开发者模式设置](image/Tampermonkey安装与脚本配置指南/1752736211862.png)

1. **打开Edge Add-ons商店**

   - 在Edge浏览器中访问：[Microsoft Edge Add-ons - Tampermonkey](https://microsoftedge.microsoft.com/addons/detail/tampermonkey/iikmkjmpaadaobahmlepeloendndfphd)
   - 或者在Edge中搜索"Tampermonkey"
2. **安装插件**

   - 点击"获取"按钮
   - 在弹出的确认对话框中点击"添加扩展"
   - 等待安装完成
3. **验证安装**

   - 安装完成后，浏览器右上角会出现Tampermonkey图标（🐒）
   - 点击图标确认插件正常工作

---

## 📝 添加自动注册脚本

### 方法一：直接导入脚本文件

1. **打开Tampermonkey管理面板**

   - 点击浏览器右上角的Tampermonkey图标（🐒）
   - 选择"管理面板"
2. **导入脚本**

   - 在管理面板中点击"实用工具"标签
   - 在"文件"部分，点击"选择文件"
   - 浏览并选择脚本文件：`augment_auto.js`
   - 点击"导入"按钮
3. **确认安装**

   - 系统会显示脚本预览
   - 点击"安装"按钮确认

### 方法二：手动创建脚本

1. **创建新脚本**

   - 点击Tampermonkey图标（🐒）
   - 选择"创建新脚本..."
2. **复制脚本内容**

   - 删除编辑器中的默认内容
   - 打开文件 `augment_auto.js`
   - 复制全部内容并粘贴到Tampermonkey编辑器中
3. **保存脚本**

   - 按 `Ctrl + S` 保存脚本
   - 或点击"文件" → "保存"

### 方法三：通过URL安装（如果脚本托管在网上）

1. **获取脚本URL**

   - 如果脚本已上传到GitHub或其他平台，复制原始文件URL
2. **安装脚本**

   - 点击Tampermonkey图标
   - 选择"添加新脚本..."
   - 在地址栏输入脚本URL
   - 按回车键自动安装

---

## � 在Cursor中安装Augment插件

### 什么是Augment插件

Augment插件是一个强大的AI代码助手扩展，可以在Cursor编辑器中提供智能代码补全、代码分析和自动化开发功能。

### 安装步骤

#### 方法一：通过VSIX文件安装（推荐）

1. **准备插件文件**

   - 确保您有 `augment.vscode-augment-0.496.2.vsix` 文件
   - 将文件放在容易访问的位置
2. **打开Cursor编辑器**

   - 启动Cursor应用程序
   - 确保编辑器完全加载
3. **安装插件**

   - 按 `Ctrl + Shift + P` (Windows/Linux) 或 `Cmd + Shift + P` (Mac) 打开命令面板
   - 输入 "Extensions: Install from VSIX..."
   - 选择该命令并按回车
4. **选择VSIX文件**

   - 在文件浏览器中导航到 `augment.vscode-augment-0.496.2.vsix` 文件位置
   - 选择文件并点击"安装"
5. **确认安装**

   - 等待安装完成
   - 重启Cursor编辑器以确保插件正常加载

#### 方法二：通过扩展面板安装

1. **打开扩展面板**

   - 点击左侧活动栏中的扩展图标（四个方块图标）
   - 或按 `Ctrl + Shift + X` (Windows/Linux) 或 `Cmd + Shift + X` (Mac)
2. **安装本地扩展**

   - 点击扩展面板右上角的"..."菜单
   - 选择"从VSIX安装..."
   - 浏览并选择 `augment.vscode-augment-0.496.2.vsix` 文件
3. **完成安装**

   - 点击"安装"按钮
   - 等待安装过程完成

### 验证安装

1. **检查扩展列表**

   - 在扩展面板中查看"已安装"部分
   - 确认"Augment"扩展出现在列表中且状态为"已启用"
2. **检查功能**

   - 打开一个代码文件
   - 查看是否有Augment相关的功能和提示
   - 检查状态栏是否显示Augment相关信息

### 配置Augment插件

1. **访问设置**

   - 按 `Ctrl + ,` (Windows/Linux) 或 `Cmd + ,` (Mac) 打开设置
   - 搜索"Augment"查看相关配置选项
2. **基本配置**

   - 设置API密钥（如果需要）
   - 配置代码补全偏好
   - 调整AI助手行为设置
3. **快捷键设置**

   - 按 `Ctrl + K, Ctrl + S` 打开快捷键设置
   - 搜索"Augment"查看和自定义相关快捷键

### 使用Augment功能

1. **AI代码补全**

   - 在编写代码时，Augment会自动提供智能建议
   - 使用 `Tab` 键接受建议
2. **代码分析**

   - Augment会实时分析代码质量
   - 在问题面板中查看建议和警告
3. **AI聊天助手**

   - 使用快捷键或命令面板访问AI聊天功能
   - 询问代码相关问题或请求帮助

---

## ⚙️ Cursor编辑器界面优化设置

### 活动栏布局调整

在安装完Cursor编辑器后，您可能会发现其界面布局与VSCode有所不同。Cursor默认将活动栏（Activity Bar）放置在窗口顶部，而不是像VSCode那样放在左侧。为了获得更熟悉的开发体验，建议将活动栏调整为侧边布局。

### 设置步骤

#### 第一步：打开设置页面

1. **通过菜单栏访问**

   - 点击 `菜单栏` → `文件` → `首选项` → `设置`
2. **通过快捷键访问**

   - 按 `Ctrl + ,` (Windows/Linux) 或 `Cmd + ,` (Mac)

#### 第二步：搜索配置项

1. **定位设置项**

   - 在设置页面搜索框中输入 `workbench.activityBar.orientation`
2. **修改配置值**

   - 找到该配置项后，将值从 `horizontal` 改为 `vertical`
   - 或者在下拉菜单中选择 `vertical`

#### 第三步：重启应用

1. **保存更改**

   - 确保所有代码更改已保存
2. **重启Cursor**

   - Cursor会提示需要重启才能生效
   - 点击提示中的 `【重启】` 按钮
   - 或手动关闭并重新打开Cursor

### 配置详解

- **配置项名称**: `workbench.activityBar.orientation`
- **默认值**: `horizontal` (水平布局，活动栏显示在顶部)
- **推荐值**: `vertical` (垂直布局，活动栏显示在侧边)
- **生效方式**: 需要重启编辑器

### Augment聊天面板位置调整

安装Augment插件后，您可以根据个人使用习惯调整AI聊天面板的位置，以获得最佳的开发体验。

#### 调整步骤

1. **定位Augment图标**

   - 在Cursor编辑器中找到Augment插件图标
   - 图标通常显示在活动栏或状态栏中
2. **拖动调整位置**

   - 点击并按住Augment图标
   - 将图标拖动到编辑器右侧区域
   - 松开鼠标完成位置调整
3. **验证布局效果**

   - 确认聊天面板已移动到右侧
   - 检查是否与原Cursor使用习惯匹配

#### 布局优势

**右侧布局的优点**：

- **保持代码焦点**：主编辑区域不被遮挡，代码始终可见
- **符合阅读习惯**：从左到右的视觉流程更自然
- **多屏适配**：在宽屏显示器上提供更好的空间利用
- **减少切换**：AI聊天和代码编辑可以同时进行

**与原Cursor习惯匹配**：

- 保持与Cursor原生聊天窗口相同的位置
- 维持用户已建立的操作习惯
- 减少学习成本和适应时间

### 其他界面优化建议

1. **主题设置**

   - 选择适合的颜色主题以提高代码可读性
   - 推荐使用深色主题减少眼部疲劳
2. **字体配置**

   - 调整合适的字体大小和字体族
   - 推荐使用等宽字体如 Fira Code、JetBrains Mono
3. **侧边栏宽度**

   - 根据屏幕大小调整侧边栏宽度
   - 确保文件树和扩展面板有足够显示空间
4. **面板大小调整**

   - 拖动面板边界调整聊天窗口大小
   - 根据对话内容长度灵活调整宽度
5. **快捷键自定义**

   - 为Augment功能设置便捷的快捷键
   - 提高AI助手的访问效率

### 效果对比

#### 活动栏位置对比

**调整前（默认）**:

- 活动栏位于窗口顶部
- 可能与VSCode使用习惯不符
- 垂直空间利用率较低

**调整后（推荐）**:

- 活动栏位于窗口左侧
- 与VSCode布局保持一致
- 更符合传统IDE使用习惯
- 更好的屏幕空间利用

#### Augment面板位置对比

**默认位置**:

- AI聊天面板可能位于左侧或底部
- 可能遮挡代码编辑区域
- 需要频繁切换视图焦点

**右侧位置（推荐）**:

- AI聊天面板固定在编辑器右侧
- 代码和AI对话同时可见
- 符合Cursor原生聊天窗口习惯
- 提供更流畅的AI辅助编程体验

---

## �🚀 脚本使用说明

### 脚本功能

- **自动邮箱生成**：使用随机生成的邮箱地址
- **自动验证码获取**：从临时邮箱服务获取验证码
- **自动表单填写**：自动填写注册表单
- **实时日志显示**：在页面右下角显示操作日志

### 使用步骤

1. **访问注册页面**

   - 点击插件界面“sign   in",软件会自动跳转打开 AugmentCode 官网 https://www.augmentcode.com/，点击右上角 “sign  in ”进入注册页面。
   - 脚本会自动检测页面类型
2. **开始自动注册**

   - 在注册页面，首先点击人机验证 Verify；
   - 然后点击右下角日志窗口中的"开始注册"按钮
   - 脚本会自动完成整个注册流程
3. **监控进度**

   - 通过右下角的日志窗口查看实时进度
   - 绿色消息表示成功，红色表示错误，黄色表示警告

### 日志窗口功能

- **最小化**：点击"_"按钮最小化日志窗口
- **清除日志**：点击"清除"按钮清空日志内容
- **关注提示**：显示技术资源获取信息

---

## ❓ 常见问题解决

### 脚本无法运行

1. **检查脚本状态**

   - 确保脚本在Tampermonkey中处于启用状态
   - 检查脚本的@match规则是否匹配当前网站
2. **检查权限设置**

   - 确保Tampermonkey有足够的权限访问网站
   - 检查浏览器是否阻止了脚本执行

### 验证码获取失败

1. **网络连接问题**

   - 检查网络连接是否正常
   - 确保可以访问临时邮箱服务
2. **邮箱服务问题**

   - 临时邮箱服务可能暂时不可用
   - 可以稍后重试

### 脚本更新

1. **手动更新**

   - 重新导入最新的脚本文件
   - 或在Tampermonkey中编辑现有脚本
2. **自动更新**

   - 如果脚本包含更新URL，Tampermonkey会自动检查更新

### Augment插件问题

1. **插件无法安装**

   - 确保VSIX文件完整且未损坏
   - 检查Cursor版本是否兼容
   - 尝试重启Cursor后重新安装
2. **插件安装后无法使用**

   - 检查插件是否已启用
   - 查看Cursor的输出面板是否有错误信息
   - 尝试禁用其他可能冲突的扩展
3. **AI功能无响应**

   - 检查网络连接是否正常
   - 验证API密钥配置是否正确
   - 查看Cursor的开发者工具控制台
4. **代码补全不工作**

   - 确保文件类型被Augment支持
   - 检查插件设置中的代码补全选项
   - 重启Cursor编辑器
5. **插件更新问题**

   - 卸载旧版本后重新安装新版本
   - 清除Cursor的扩展缓存
   - 检查是否有配置文件冲突

---

## 📞 技术支持

### Tampermonkey脚本支持

如遇到脚本问题，可以：

- 检查浏览器控制台的错误信息
- 查看Tampermonkey的日志输出
- 关注小红书「爱编程的小羊」获取更多技术资源

### Augment插件支持

如遇到插件问题，可以：

- 查看Cursor的输出面板和开发者工具
- 检查插件的设置和配置
- 访问Augment官方文档和社区
- 联系Augment技术支持团队

---

## ⚠️ 注意事项

### Tampermonkey脚本注意事项

1. **使用责任**：请确保遵守相关网站的服务条款
2. **隐私保护**：脚本使用临时邮箱，注意保护个人隐私
3. **合法使用**：仅用于合法的自动化测试和学习目的
4. **定期更新**：根据网站变化及时更新脚本内容

### Augment插件注意事项

1. **版本兼容性**：确保插件版本与Cursor编辑器版本兼容
2. **资源使用**：AI功能可能消耗较多系统资源和网络带宽
3. **数据安全**：注意代码隐私，避免在敏感项目中使用
4. **许可证遵守**：遵守Augment插件的使用许可证和条款
5. **备份重要**：在使用AI建议前，确保代码已备份

---

## 📚 完整配置总结

本指南涵盖了完整的开发环境配置流程：

### 🔧 浏览器端配置

1. **安装Tampermonkey插件** - 在Chrome或Edge浏览器中安装用户脚本管理器
2. **配置自动注册脚本** - 导入 `augment_auto.js` 脚本实现自动化注册

### 💻 编辑器端配置

1. **安装Augment插件** - 在Cursor中安装 `augment.vscode-augment-0.496.2.vsix` 扩展
2. **优化活动栏布局** - 调整活动栏为侧边显示，获得熟悉的VSCode体验
3. **调整AI面板位置** - 拖动Augment图标到右侧，匹配原Cursor使用习惯

### 🎯 配置完成后的效果

- **浏览器端**：自动化注册流程，提高账号创建效率
- **编辑器端**：AI辅助编程，智能代码补全和分析
- **界面优化**：熟悉的VSCode风格布局，提升开发舒适度
- **AI面板优化**：右侧聊天面板，代码与AI对话同屏显示

### 🚀 下一步建议

1. 熟悉Augment插件的各项AI功能
2. 根据个人习惯调整更多编辑器设置
3. 探索Tampermonkey的其他实用脚本
4. 关注相关工具的更新和新功能

---

## 📖 扩展阅读

- Cursor聊天窗口介绍与使用
- Cursor配置优化指南
- AI编程工具对比分析
- Tampermonkey高级脚本开发

---

*最后更新时间：2025年7月*
*内容整合自多个技术教程和官方文档*
