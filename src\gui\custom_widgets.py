"""
自定义部件
"""
import tkinter as tk
from tkinter import ttk
import os
from PIL import Image, ImageTk
from src.gui.theme_manager import theme_manager


class ModernButton(ttk.Button):
    """现代化按钮，支持圆角、图标和悬停效果"""
    
    def __init__(self, master, text='', icon=None, style='TButton', command=None, **kwargs):
        """初始化现代化按钮"""
        self.icon_image = None
        self.photo_image = None
        
        # 处理图标
        if icon:
            self._prepare_icon(icon)
            super().__init__(master, text=text, image=self.photo_image, 
                           compound=tk.LEFT, style=style, command=command, **kwargs)
        else:
            super().__init__(master, text=text, style=style, command=command, **kwargs)
        
        # 绑定鼠标事件
        self.bind('<Enter>', self._on_enter)
        self.bind('<Leave>', self._on_leave)
    
    def _prepare_icon(self, icon_path):
        """准备图标"""
        try:
            # 检查图标路径是否存在
            if not os.path.exists(icon_path):
                # 尝试在相对路径中查找
                base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
                icon_path = os.path.join(base_dir, 'image', 'icons', icon_path)
                
                # 如果仍然不存在，则返回
                if not os.path.exists(icon_path):
                    return
            
            # 加载图标
            self.icon_image = Image.open(icon_path)
            
            # 调整图标大小
            icon_size = 16  # 默认图标大小
            self.icon_image = self.icon_image.resize((icon_size, icon_size), Image.LANCZOS)
            
            # 转换为PhotoImage
            self.photo_image = ImageTk.PhotoImage(self.icon_image)
        except Exception as e:
            print(f"加载图标失败: {e}")
    
    def _on_enter(self, event):
        """鼠标进入效果"""
        # 在ttk.Style中已经处理了悬停效果，这里可以添加额外的效果
        self.state(['active'])
    
    def _on_leave(self, event):
        """鼠标离开效果"""
        # 恢复正常状态
        self.state(['!active'])


class VerificationCodeDisplay(tk.Frame):
    """验证码显示框，带有美化边框和字体"""
    
    def __init__(self, master, **kwargs):
        """初始化验证码显示框"""
        super().__init__(master, **kwargs)
        self.configure(bg=theme_manager.colors['bg_light'])
        
        # 创建验证码标签和显示区域
        self.title_label = ttk.Label(self, text="当前验证码", 
                                    style='Subtitle.TLabel')
        
        # 创建验证码显示框架 - 使用圆角和阴影效果
        self.code_frame = tk.Frame(self, 
                                  bg=theme_manager.colors['white'],
                                  highlightbackground=theme_manager.colors['primary_light'],
                                  highlightthickness=2,
                                  relief='ridge',
                                  padx=15, pady=15)
        
        # 创建验证码标签 - 使用更大、更清晰的字体
        self.code_label = ttk.Label(self.code_frame, 
                                   text="暂无验证码",
                                   font=(theme_manager.monospace_font, 16, ),
                                   foreground=theme_manager.colors['text_secondary'])
        
        # 创建复制按钮
        self.copy_button = ModernButton(self, text="复制", 
                                      style='Link.TButton',
                                      command=self._copy_to_clipboard)
        
        # 布局
        self.title_label.pack(anchor='w', pady=(0, 5))
        self.code_frame.pack(fill='x', expand=True)
        self.code_label.pack(pady=10)
        self.copy_button.pack(anchor='e', pady=(5, 0))
    
    def set_code(self, code):
        """设置验证码内容"""
        if not code or code == "":
            self.code_label.configure(text="暂无验证码", 
                                    foreground=theme_manager.colors['text_secondary'])
            self.copy_button.configure(state='disabled')
        else:
            # 使用更醒目但不刺眼的颜色
            self.code_label.configure(text=code, 
                                    foreground=theme_manager.colors['primary_dark'])
            self.copy_button.configure(state='normal')
            
            # 添加视觉强调 - 改变边框颜色
            self.code_frame.configure(highlightbackground=theme_manager.colors['success'])
    
    def _copy_to_clipboard(self):
        """复制验证码到剪贴板"""
        code = self.code_label.cget('text')
        if code and code != "暂无验证码":
            self.clipboard_clear()
            self.clipboard_append(code)
            
            # 显示复制成功的视觉反馈
            original_text = self.copy_button.cget('text')
            self.copy_button.configure(text="已复制!", 
                                     foreground=theme_manager.colors['success'])
            
            # 2秒后恢复原始文本
            self.after(2000, lambda: self.copy_button.configure(
                text=original_text, 
                foreground=theme_manager.colors['primary']))


class ModernEntry(ttk.Entry):
    """现代化输入框，支持验证和错误提示"""
    
    def __init__(self, master, validate_func=None, error_var=None, **kwargs):
        """初始化现代化输入框"""
        super().__init__(master, **kwargs)
        
        self.validate_func = validate_func
        self.error_var = error_var
        
        # 绑定验证事件
        if validate_func:
            self.bind('<FocusOut>', self._validate)
            self.bind('<KeyRelease>', self._on_key_release)
    
    def _validate(self, event=None):
        """验证输入内容"""
        if self.validate_func:
            value = self.get()
            is_valid, message = self.validate_func(value)
            
            if not is_valid:
                # 显示错误样式
                self.state(['invalid'])
                if self.error_var:
                    self.error_var.set(message)
            else:
                # 恢复正常样式
                self.state(['!invalid'])
                if self.error_var:
                    self.error_var.set('')
    
    def _on_key_release(self, event=None):
        """按键释放事件"""
        # 如果按下Enter键，执行验证
        if event.keysym == 'Return':
            self._validate()


class StyledTreeview(ttk.Treeview):
    """样式化的树形视图，用于显示表格数据"""
    
    def __init__(self, master, columns, show='headings', **kwargs):
        """初始化样式化的树形视图"""
        super().__init__(master, columns=columns, show=show, **kwargs)
        
        # 设置列标题
        for col in columns:
            self.heading(col, text=col)
            self.column(col, anchor='center')
        
        # 设置交替行背景色
        self.tag_configure('odd', background=theme_manager.colors['bg_light'])
        self.tag_configure('even', background=theme_manager.colors['white'])
        
        # 绑定事件
        self.bind('<<TreeviewSelect>>', self._on_select)
    
    def insert_data(self, data):
        """插入数据"""
        # 清除现有数据
        for item in self.get_children():
            self.delete(item)
        
        # 插入新数据
        for i, row in enumerate(data):
            tag = 'even' if i % 2 == 0 else 'odd'
            self.insert('', 'end', values=row, tags=(tag,))
    
    def _on_select(self, event=None):
        """选择事件"""
        # 可以在这里添加选择行的处理逻辑
        pass