#!/usr/bin/env python3
"""
测试只包含验证码接收功能的应用
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_imports():
    """测试导入功能"""
    try:
        from src.app import Application
        print("✓ 成功导入Application类")
        
        from src.gui.simple_main_window import SimpleMainWindow
        print("✓ 成功导入SimpleMainWindow类")
        
        from src.gui.simple_verification_panel import SimpleVerificationPanel
        print("✓ 成功导入SimpleVerificationPanel类")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_app_creation():
    """测试应用创建"""
    try:
        from src.app import Application
        app = Application()
        print("✓ 成功创建Application实例")
        
        # 测试配置加载
        app._load_config()
        print("✓ 成功加载配置")
        
        return True
    except Exception as e:
        print(f"❌ 应用创建失败: {e}")
        return False

def test_window_structure():
    """测试窗口结构"""
    try:
        from src.app import Application
        app = Application()
        app._load_config()
        
        # 创建主窗口但不运行
        from src.gui.simple_main_window import SimpleMainWindow
        main_window = SimpleMainWindow(app)
        
        # 检查是否只有验证码面板
        has_verification_panel = hasattr(main_window, 'verification_panel')
        has_email_panel = hasattr(main_window, 'email_panel')
        has_notebook = hasattr(main_window, 'notebook')
        
        print(f"✓ 验证码面板存在: {has_verification_panel}")
        print(f"✓ 邮箱面板隐藏: {not has_email_panel}")
        print(f"✓ 标签页导航隐藏: {not has_notebook}")
        
        if has_verification_panel and not has_email_panel:
            print("✓ 窗口结构正确：只包含验证码接收功能")
            return True
        else:
            print("❌ 窗口结构不正确")
            return False
            
    except Exception as e:
        print(f"❌ 窗口结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试只包含验证码接收功能的应用...")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_imports),
        ("应用创建测试", test_app_creation),
        ("窗口结构测试", test_window_structure),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name}失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用已成功优化为只包含验证码接收功能")
    else:
        print("⚠️  部分测试失败，请检查代码")

if __name__ == "__main__":
    main()
