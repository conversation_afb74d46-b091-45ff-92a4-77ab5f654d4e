"""
工作流程管理器
"""
import threading
import time
from typing import Optional, Callable, List
from src.models import EmailInfo
from src.exceptions import NetworkError, VerificationCodeError


class WorkflowManager:
    """工作流程管理器"""
    
    def __init__(self, app):
        """初始化工作流程管理器"""
        self.app = app
        self.current_workflow = None
        self.is_running = False
    
    def run_auto_registration_workflow(self, on_complete: Optional[Callable] = None):
        """运行自动注册工作流程"""
        if self.is_running:
            self.app.log_manager.add_log("已有工作流程正在运行", "WARNING")
            return False
        
        self.is_running = True
        self.app.log_manager.add_log("开始自动注册工作流程", "INFO")
        
        def _workflow():
            try:
                # 步骤1：生成邮箱
                self.app.log_manager.add_log("步骤1：生成邮箱", "INFO")
                self.app.main_window.status_bar.set_status("正在生成邮箱...", "INFO")
                
                if not self.app.email_generator:
                    raise Exception("邮箱生成器未初始化，请先配置应用")
                
                email_info = self.app.email_generator.generate_single_email()
                
                # 更新邮箱面板
                if self.app.main_window:
                    self.app.main_window.email_panel.add_emails([email_info])
                
                self.app.log_manager.add_log(f"成功生成邮箱: {email_info.address}", "SUCCESS")
                self.app.main_window.status_bar.set_status("邮箱生成完成", "SUCCESS")
                
                # 等待用户使用邮箱注册
                self.app.log_manager.add_log("请使用生成的邮箱在目标网站注册，然后点击获取验证码", "INFO")
                
                # 步骤2：获取验证码（需要用户手动触发）
                # 这一步由用户点击"获取验证码"按钮触发
                
                # 工作流程完成
                self.app.log_manager.add_log("自动注册工作流程准备就绪，请继续操作", "SUCCESS")
                
                if on_complete:
                    on_complete(email_info)
                
            except Exception as e:
                self.app.log_manager.add_log(f"工作流程执行失败: {str(e)}", "ERROR")
                self.app.main_window.status_bar.set_status("工作流程失败", "ERROR")
            finally:
                self.is_running = False
        
        # 在后台线程中执行
        self.current_workflow = threading.Thread(target=_workflow, daemon=True)
        self.current_workflow.start()
        
        return True
    
    def run_batch_verification_workflow(self, count: int = 5, interval: int = 10,
                                      on_complete: Optional[Callable] = None):
        """运行批量验证码获取工作流程"""
        if self.is_running:
            self.app.log_manager.add_log("已有工作流程正在运行", "WARNING")
            return False
        
        self.is_running = True
        self.app.log_manager.add_log(f"开始批量验证码获取工作流程 (数量: {count}, 间隔: {interval}秒)", "INFO")
        
        def _workflow():
            codes = []
            try:
                if not self.app.tempmail_service:
                    raise Exception("临时邮箱服务未初始化，请先配置应用")
                
                # 测试连接
                self.app.main_window.status_bar.set_status("正在测试连接...", "INFO")
                if not self.app.tempmail_service.test_connection():
                    raise NetworkError("无法连接到临时邮箱服务，请检查配置和网络连接")
                
                # 批量获取验证码
                for i in range(count):
                    self.app.log_manager.add_log(f"正在获取第 {i+1}/{count} 个验证码...", "INFO")
                    self.app.main_window.status_bar.set_status(f"正在获取验证码 {i+1}/{count}...", "INFO")
                    
                    try:
                        code = self.app.tempmail_service.get_verification_code_with_retry()
                        if code:
                            codes.append(code)
                            self.app.log_manager.add_log(f"成功获取验证码: {code}", "SUCCESS")
                            
                            # 更新验证码面板
                            if self.app.main_window:
                                self.app.main_window.verification_panel.set_verification_code(code)
                        else:
                            self.app.log_manager.add_log("未能获取验证码", "WARNING")
                    except Exception as e:
                        self.app.log_manager.add_log(f"获取验证码失败: {str(e)}", "ERROR")
                    
                    # 如果不是最后一个，等待指定的间隔时间
                    if i < count - 1:
                        self.app.log_manager.add_log(f"等待 {interval} 秒后继续...", "INFO")
                        time.sleep(interval)
                
                # 工作流程完成
                success_count = len(codes)
                self.app.log_manager.add_log(
                    f"批量验证码获取完成，成功: {success_count}/{count}", 
                    "SUCCESS" if success_count > 0 else "WARNING"
                )
                self.app.main_window.status_bar.set_status(
                    f"批量验证码获取完成 ({success_count}/{count})", 
                    "SUCCESS" if success_count > 0 else "WARNING"
                )
                
                if on_complete:
                    on_complete(codes)
                
            except Exception as e:
                self.app.log_manager.add_log(f"工作流程执行失败: {str(e)}", "ERROR")
                self.app.main_window.status_bar.set_status("工作流程失败", "ERROR")
            finally:
                self.is_running = False
        
        # 在后台线程中执行
        self.current_workflow = threading.Thread(target=_workflow, daemon=True)
        self.current_workflow.start()
        
        return True
    
    def stop_current_workflow(self):
        """停止当前工作流程"""
        if not self.is_running:
            self.app.log_manager.add_log("没有正在运行的工作流程", "WARNING")
            return False
        
        # 我们不能直接停止线程，但可以设置标志位
        self.is_running = False
        self.app.log_manager.add_log("已请求停止工作流程", "INFO")
        self.app.main_window.status_bar.set_status("正在停止工作流程...", "WARNING")
        
        return True