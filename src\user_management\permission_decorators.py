"""
权限检查装饰器
用于在执行操作前检查用户权限
"""
from functools import wraps
from typing import Callable, Any
import tkinter.messagebox as messagebox

from .user_permission_system import UserPermissionManager


class PermissionChecker:
    """权限检查器"""
    
    def __init__(self, permission_manager: UserPermissionManager):
        """初始化权限检查器"""
        self.permission_manager = permission_manager
    
    def require_login(self, func: Callable) -> Callable:
        """要求用户登录的装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not self.permission_manager.current_user:
                messagebox.showerror("权限错误", "请先登录用户账号")
                return None
            return func(*args, **kwargs)
        return wrapper
    
    def check_email_limit(self, func: Callable) -> Callable:
        """检查邮箱注册限制的装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not self.permission_manager.current_user:
                messagebox.showerror("权限错误", "请先登录用户账号")
                return None
            
            # 检查邮箱注册限制
            allowed, reason, used, limit = self.permission_manager.check_email_limit()
            if not allowed:
                if limit == -1:
                    limit_text = "无限制"
                else:
                    limit_text = f"{limit}个"
                messagebox.showwarning("限制提醒", 
                                     f"{reason}\n当前已使用: {used}\n限制数量: {limit_text}")
                return None
            
            # 检查操作频率限制
            rate_allowed, rate_reason, wait_time = self.permission_manager.check_rate_limit()
            if not rate_allowed:
                messagebox.showwarning("频率限制", rate_reason)
                return None
            
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 如果操作成功，记录使用
            if result is not None:
                self.permission_manager.record_email_generation()
            
            return result
        return wrapper
    
    def check_verification_limit(self, func: Callable) -> Callable:
        """检查验证码获取限制的装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not self.permission_manager.current_user:
                messagebox.showerror("权限错误", "请先登录用户账号")
                return None
            
            # 检查验证码获取限制
            allowed, reason, used, limit = self.permission_manager.check_verification_limit()
            if not allowed:
                if limit == -1:
                    limit_text = "无限制"
                else:
                    limit_text = f"{limit}次"
                messagebox.showwarning("限制提醒", 
                                     f"{reason}\n当前已使用: {used}\n限制次数: {limit_text}")
                return None
            
            # 检查操作频率限制
            rate_allowed, rate_reason, wait_time = self.permission_manager.check_rate_limit()
            if not rate_allowed:
                messagebox.showwarning("频率限制", rate_reason)
                return None
            
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 如果操作成功，记录使用
            if result is not None:
                self.permission_manager.record_verification_request()
            
            return result
        return wrapper
    
    def require_feature(self, feature_name: str):
        """要求特定功能权限的装饰器"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                if not self.permission_manager.current_user:
                    messagebox.showerror("权限错误", "请先登录用户账号")
                    return None
                
                user_role = self.permission_manager.current_user.role
                permission = self.permission_manager.permissions.get(user_role)
                
                if not permission:
                    messagebox.showerror("权限错误", "用户权限配置不存在")
                    return None
                
                # 检查是否有该功能权限
                if feature_name not in permission.features and "all" not in permission.features:
                    messagebox.showerror("权限不足", f"您的账号类型不支持 {feature_name} 功能")
                    return None
                
                return func(*args, **kwargs)
            return wrapper
        return decorator
    
    def show_user_stats(self):
        """显示用户统计信息"""
        if not self.permission_manager.current_user:
            messagebox.showinfo("用户状态", "当前未登录")
            return
        
        stats = self.permission_manager.get_user_stats()
        if not stats:
            messagebox.showerror("错误", "无法获取用户统计信息")
            return
        
        user_info = stats['user_info']
        limits = stats['limits']
        
        # 构建统计信息文本
        info_text = f"用户: {user_info['username']}\n"
        info_text += f"角色: {user_info['role']}\n"
        info_text += f"注册时间: {user_info['created_at'][:19]}\n\n"
        
        # 邮箱限制信息
        email_limit = limits['email']
        if email_limit['limit'] == -1:
            email_limit_text = "无限制"
        else:
            email_limit_text = f"{email_limit['limit']}个"
        
        info_text += f"邮箱注册: {email_limit['used']}/{email_limit_text}\n"
        
        # 验证码限制信息
        verification_limit = limits['verification']
        if verification_limit['limit'] == -1:
            verification_limit_text = "无限制"
        else:
            verification_limit_text = f"{verification_limit['limit']}次"
        
        info_text += f"验证码获取: {verification_limit['used']}/{verification_limit_text}\n"
        
        # 频率限制信息
        rate_limit = limits['rate_limit']
        if rate_limit['limit_seconds'] == 0:
            info_text += "操作频率: 无限制\n"
        else:
            info_text += f"操作间隔: {rate_limit['limit_seconds']}秒\n"
        
        # 可用功能
        features = stats['features']
        if "all" in features:
            info_text += "\n可用功能: 全部功能"
        else:
            feature_names = {
                'basic_email_generation': '基础邮箱生成',
                'verification_code': '验证码获取',
                'batch_generation': '批量生成',
                'custom_domains': '自定义域名'
            }
            feature_list = [feature_names.get(f, f) for f in features]
            info_text += f"\n可用功能: {', '.join(feature_list)}"
        
        messagebox.showinfo("用户统计", info_text)


# 全局权限检查器实例
_permission_checker = None


def get_permission_checker(permission_manager: UserPermissionManager = None) -> PermissionChecker:
    """获取权限检查器实例"""
    global _permission_checker
    if _permission_checker is None or permission_manager is not None:
        if permission_manager is None:
            from .user_permission_system import UserPermissionManager
            permission_manager = UserPermissionManager()
        _permission_checker = PermissionChecker(permission_manager)
    return _permission_checker


# 便捷装饰器函数
def require_login(func: Callable) -> Callable:
    """要求用户登录"""
    return get_permission_checker().require_login(func)


def check_email_limit(func: Callable) -> Callable:
    """检查邮箱注册限制"""
    return get_permission_checker().check_email_limit(func)


def check_verification_limit(func: Callable) -> Callable:
    """检查验证码获取限制"""
    return get_permission_checker().check_verification_limit(func)


def require_feature(feature_name: str):
    """要求特定功能权限"""
    return get_permission_checker().require_feature(feature_name)


def show_user_stats():
    """显示用户统计信息"""
    return get_permission_checker().show_user_stats()
