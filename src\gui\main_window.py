"""
主窗口
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Optional
import os
from src.gui.email_panel import EmailPanel
from src.gui.config_panel import ConfigPanel
from src.gui.verification_panel import VerificationPanel
from src.gui.log_panel import LogPanel
from src.gui.status_bar import StatusBar
from src.gui.theme_manager import theme_manager


class MainWindow:
    """主窗口类"""
    
    def __init__(self, app):
        self.app = app  # 应用程序实例
        self.root = tk.Tk()
        
        self._setup_window()
        self._create_widgets()
        self._setup_logging()
    
    def _setup_window(self):
        """设置窗口属性"""
        self.root.title("AugmentCode自动注册助手")
        self.root.geometry("900x700")  # 增加默认窗口大小
        self.root.minsize(700, 500)    # 增加最小窗口大小

        # 应用主题
        self.style = theme_manager.apply_theme(self.root)

        # 设置窗口图标（如果有的话）
        try:
            icon_path = os.path.join(os.path.dirname(__file__), '..', '..', 'icon.ico')
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception:
            pass

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

        # 绑定窗口大小变化事件
        self.root.bind('<Configure>', self._on_window_resize)

        # 居中显示窗口
        self._center_window()

        # 初始化响应式布局变量
        self._last_width = 900
        self._last_height = 700
        self._is_compact_mode = False
    
    def _center_window(self):
        """居中显示窗口"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def _on_window_resize(self, event):
        """窗口大小变化事件处理"""
        # 只处理主窗口的resize事件
        if event.widget != self.root:
            return

        current_width = self.root.winfo_width()
        current_height = self.root.winfo_height()

        # 检查是否需要切换到紧凑模式
        compact_threshold = 800  # 紧凑模式阈值
        should_be_compact = current_width < compact_threshold

        if should_be_compact != self._is_compact_mode:
            self._is_compact_mode = should_be_compact
            self._apply_responsive_layout()

        self._last_width = current_width
        self._last_height = current_height

    def _apply_responsive_layout(self):
        """应用响应式布局"""
        if self._is_compact_mode:
            # 紧凑模式：减少内边距，调整组件大小
            self._apply_compact_layout()
        else:
            # 正常模式：恢复默认布局
            self._apply_normal_layout()

    def _apply_compact_layout(self):
        """应用紧凑布局"""
        # 减少主框架的内边距
        for child in self.root.winfo_children():
            if isinstance(child, ttk.Frame):
                child.pack_configure(padx=5, pady=5)

        # 通知各个面板切换到紧凑模式
        if hasattr(self, 'email_panel'):
            self.email_panel.set_compact_mode(True)
        if hasattr(self, 'verification_panel'):
            self.verification_panel.set_compact_mode(True)
        if hasattr(self, 'config_panel'):
            self.config_panel.set_compact_mode(True)
        if hasattr(self, 'log_panel'):
            self.log_panel.set_compact_mode(True)

    def _apply_normal_layout(self):
        """应用正常布局"""
        # 恢复主框架的内边距
        for child in self.root.winfo_children():
            if isinstance(child, ttk.Frame):
                child.pack_configure(padx=10, pady=10)

        # 通知各个面板切换到正常模式
        if hasattr(self, 'email_panel'):
            self.email_panel.set_compact_mode(False)
        if hasattr(self, 'verification_panel'):
            self.verification_panel.set_compact_mode(False)
        if hasattr(self, 'config_panel'):
            self.config_panel.set_compact_mode(False)
        if hasattr(self, 'log_panel'):
            self.log_panel.set_compact_mode(False)
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个面板
        self.email_panel = EmailPanel(self.notebook, self)
        self.verification_panel = VerificationPanel(self.notebook, self)
        self.config_panel = ConfigPanel(self.notebook, self)
        self.log_panel = LogPanel(self.notebook, self)
        
        # 添加标签页
        self.notebook.add(self.email_panel.frame, text="邮箱生成")
        self.notebook.add(self.verification_panel.frame, text="验证码获取")
        self.notebook.add(self.config_panel.frame, text="配置设置")
        self.notebook.add(self.log_panel.frame, text="日志查看")
        
        # 创建状态栏
        self.status_bar = StatusBar(main_frame, self)
        self.status_bar.frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))
    
    def _check_first_run(self):
        """检查是否首次运行"""
        # 如果是首次运行，显示配置页面
        if self.app.app_config and not self.app.app_config.temp_mail.username:
            self.notebook.select(2)  # 选择配置标签页
    
    def _setup_logging(self):
        """设置日志"""
        # 添加日志回调，更新日志面板
        self.app.log_manager.add_callback(self._on_log_added)
        
        # 检查是否首次运行
        self._check_first_run()
    
    def _on_log_added(self, log_entry):
        """日志添加回调"""
        # 更新日志面板
        if hasattr(self, 'log_panel'):
            self.log_panel.refresh_logs()
        
        # 更新状态栏
        if hasattr(self, 'status_bar') and log_entry:
            self.status_bar.set_status(log_entry.message, log_entry.level)
    
    def save_config(self, config):
        """保存配置"""
        return self.app.save_config(config)
    
    def generate_email(self, count: int = 1):
        """生成邮箱"""
        self.app.generate_email(count)
    
    def get_verification_code(self):
        """获取验证码"""
        self.app.get_verification_code()
    
    def _on_closing(self):
        """窗口关闭事件"""
        try:
            self.app.log_manager.add_log("应用程序关闭", "INFO")
            self.root.quit()
            self.root.destroy()
        except:
            pass
    
    def run(self):
        """运行应用"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self._on_closing()
        except Exception as e:
            messagebox.showerror("严重错误", f"应用程序发生严重错误: {str(e)}")
            self._on_closing()