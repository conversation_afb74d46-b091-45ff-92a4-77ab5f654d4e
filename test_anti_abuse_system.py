#!/usr/bin/env python3
"""
防滥用系统测试脚本
演示用户权限管理和限制功能
"""
import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from src.user_management.user_permission_system import UserPermissionManager, UserRole
from src.user_management.permission_decorators import get_permission_checker
from src.user_management.usage_monitor import get_usage_monitor


def test_user_creation_and_permissions():
    """测试用户创建和权限管理"""
    print("=" * 60)
    print("🧪 测试用户创建和权限管理")
    print("=" * 60)
    
    # 初始化权限管理器
    permission_manager = UserPermissionManager()
    permission_checker = get_permission_checker(permission_manager)
    usage_monitor = get_usage_monitor()
    
    # 创建不同角色的测试用户
    test_users = [
        ("guest_user", UserRole.GUEST, "访客用户"),
        ("basic_user", UserRole.BASIC, "基础用户"),
        ("premium_user", UserRole.PREMIUM, "高级用户"),
        ("vip_user", UserRole.VIP, "VIP用户"),
        ("admin_user", UserRole.ADMIN, "管理员用户")
    ]
    
    created_users = []
    
    for username, role, description in test_users:
        try:
            user_id = permission_manager.create_user(username, role, description)
            created_users.append((username, role))
            print(f"✅ 创建用户: {username} ({role.value}) - {description}")
        except ValueError as e:
            print(f"⚠️  用户 {username} 已存在: {e}")
            created_users.append((username, role))
    
    print(f"\n📊 总共创建/确认 {len(created_users)} 个测试用户")
    return permission_manager, created_users


def test_permission_limits(permission_manager, test_users):
    """测试权限限制功能"""
    print("\n" + "=" * 60)
    print("🔒 测试权限限制功能")
    print("=" * 60)
    
    for username, role in test_users:
        print(f"\n🧑‍💻 测试用户: {username} ({role.value})")
        print("-" * 40)
        
        # 登录用户
        user_info = permission_manager.login_user(username)
        if not user_info:
            print(f"❌ 用户 {username} 登录失败")
            continue
        
        # 获取用户统计信息
        stats = permission_manager.get_user_stats()
        if stats:
            limits = stats['limits']
            
            # 显示邮箱限制
            email_limit = limits['email']
            if email_limit['limit'] == -1:
                email_limit_text = "无限制"
            else:
                email_limit_text = f"{email_limit['limit']}个"
            print(f"📧 邮箱限制: {email_limit['used']}/{email_limit_text}")
            
            # 显示验证码限制
            verification_limit = limits['verification']
            if verification_limit['limit'] == -1:
                verification_limit_text = "无限制"
            else:
                verification_limit_text = f"{verification_limit['limit']}次"
            print(f"🔐 验证码限制: {verification_limit['used']}/{verification_limit_text}")
            
            # 显示频率限制
            rate_limit = limits['rate_limit']
            if rate_limit['limit_seconds'] == 0:
                print("⏱️  操作频率: 无限制")
            else:
                print(f"⏱️  操作间隔: {rate_limit['limit_seconds']}秒")
            
            # 显示可用功能
            features = stats['features']
            if "all" in features:
                print("🎯 可用功能: 全部功能")
            else:
                feature_names = {
                    'basic_email_generation': '基础邮箱生成',
                    'verification_code': '验证码获取',
                    'batch_generation': '批量生成',
                    'custom_domains': '自定义域名'
                }
                feature_list = [feature_names.get(f, f) for f in features]
                print(f"🎯 可用功能: {', '.join(feature_list)}")


def test_email_generation_limits(permission_manager, test_users):
    """测试邮箱生成限制"""
    print("\n" + "=" * 60)
    print("📧 测试邮箱生成限制")
    print("=" * 60)
    
    for username, role in test_users[:3]:  # 只测试前3个用户
        print(f"\n🧑‍💻 测试用户: {username} ({role.value})")
        print("-" * 40)
        
        # 登录用户
        user_info = permission_manager.login_user(username)
        if not user_info:
            continue
        
        # 模拟邮箱生成操作
        success_count = 0
        max_attempts = 10  # 最多尝试10次
        
        for i in range(max_attempts):
            # 检查邮箱生成限制
            allowed, reason, used, limit = permission_manager.check_email_limit()
            
            if not allowed:
                print(f"❌ 第{i+1}次尝试被拒绝: {reason}")
                break
            
            # 检查频率限制
            rate_allowed, rate_reason, wait_time = permission_manager.check_rate_limit()
            if not rate_allowed:
                print(f"⏳ 第{i+1}次尝试需要等待: {rate_reason}")
                if wait_time > 0 and wait_time <= 5:  # 只等待5秒以内
                    time.sleep(wait_time)
                    continue
                else:
                    break
            
            # 模拟成功的邮箱生成
            permission_manager.record_email_generation()
            success_count += 1
            print(f"✅ 第{i+1}次邮箱生成成功")
            
            # 短暂延迟避免过快操作
            time.sleep(0.1)
        
        print(f"📊 {username} 成功生成 {success_count} 个邮箱")


def test_verification_limits(permission_manager, test_users):
    """测试验证码获取限制"""
    print("\n" + "=" * 60)
    print("🔐 测试验证码获取限制")
    print("=" * 60)
    
    for username, role in test_users[:2]:  # 只测试前2个用户
        print(f"\n🧑‍💻 测试用户: {username} ({role.value})")
        print("-" * 40)
        
        # 登录用户
        user_info = permission_manager.login_user(username)
        if not user_info:
            continue
        
        # 模拟验证码获取操作
        success_count = 0
        max_attempts = 8  # 最多尝试8次
        
        for i in range(max_attempts):
            # 检查验证码获取限制
            allowed, reason, used, limit = permission_manager.check_verification_limit()
            
            if not allowed:
                print(f"❌ 第{i+1}次尝试被拒绝: {reason}")
                break
            
            # 检查频率限制
            rate_allowed, rate_reason, wait_time = permission_manager.check_rate_limit()
            if not rate_allowed:
                print(f"⏳ 第{i+1}次尝试需要等待: {rate_reason}")
                if wait_time > 0 and wait_time <= 3:  # 只等待3秒以内
                    time.sleep(wait_time)
                    continue
                else:
                    break
            
            # 模拟成功的验证码获取
            permission_manager.record_verification_request()
            success_count += 1
            print(f"✅ 第{i+1}次验证码获取成功")
            
            # 短暂延迟避免过快操作
            time.sleep(0.1)
        
        print(f"📊 {username} 成功获取 {success_count} 次验证码")


def test_usage_monitoring():
    """测试使用监控功能"""
    print("\n" + "=" * 60)
    print("📊 测试使用监控功能")
    print("=" * 60)
    
    usage_monitor = get_usage_monitor()
    
    # 模拟一些使用事件
    test_events = [
        ("user1", "test_user1", "email_generation", True),
        ("user1", "test_user1", "verification_request", True),
        ("user2", "test_user2", "email_generation", False, "配额超限"),
        ("user1", "test_user1", "login", True),
        ("user3", "test_user3", "email_generation", True),
    ]
    
    print("📝 记录测试事件...")
    for user_id, username, event_type, success, *args in test_events:
        error_message = args[0] if args else ""
        usage_monitor.record_event(
            user_id=user_id,
            username=username,
            event_type=event_type,
            success=success,
            error_message=error_message
        )
        status = "成功" if success else f"失败({error_message})"
        print(f"  📋 {username}: {event_type} - {status}")
    
    # 获取使用统计
    print("\n📈 使用统计:")
    stats = usage_monitor.get_usage_statistics(days=1)
    if stats:
        print(f"  总事件数: {stats.get('total_events', 0)}")
        print(f"  独立用户: {stats.get('unique_users', 0)}")
        print(f"  成功率: {stats.get('success_rate', 0):.2%}")
        
        event_types = stats.get('event_types', {})
        if event_types:
            print("  事件类型分布:")
            for event_type, count in event_types.items():
                print(f"    {event_type}: {count}")
    
    # 获取活跃警报
    print("\n🚨 活跃警报:")
    alerts = usage_monitor.get_active_alerts()
    if alerts:
        for alert in alerts:
            print(f"  ⚠️  {alert['username']}: {alert['description']} ({alert['severity']})")
    else:
        print("  ✅ 暂无活跃警报")


def display_summary():
    """显示测试总结"""
    print("\n" + "=" * 60)
    print("🎉 防滥用系统测试完成")
    print("=" * 60)
    
    print("\n✅ 测试完成的功能:")
    print("  🔐 用户权限管理 - 不同角色不同限制")
    print("  📧 邮箱生成限制 - 数量和频率控制")
    print("  🔐 验证码获取限制 - 次数和间隔控制")
    print("  📊 使用统计监控 - 事件记录和分析")
    print("  🚨 异常行为检测 - 自动警报生成")
    
    print("\n🎯 防滥用效果:")
    print("  ✅ 有效防止单用户大量注册")
    print("  ✅ 控制操作频率防止恶意使用")
    print("  ✅ 分级权限满足不同用户需求")
    print("  ✅ 实时监控及时发现异常")
    print("  ✅ 灵活配置适应不同场景")
    
    print("\n🚀 下一步:")
    print("  1. 运行 'python run_simple_app.py' 体验完整功能")
    print("  2. 使用管理员账号访问配置界面")
    print("  3. 查看用户统计和监控数据")
    print("  4. 根据需要调整权限配置")


def main():
    """主测试函数"""
    print("🛡️  防滥用注册功能系统测试")
    print("=" * 60)
    print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # 1. 测试用户创建和权限管理
        permission_manager, test_users = test_user_creation_and_permissions()
        
        # 2. 测试权限限制功能
        test_permission_limits(permission_manager, test_users)
        
        # 3. 测试邮箱生成限制
        test_email_generation_limits(permission_manager, test_users)
        
        # 4. 测试验证码获取限制
        test_verification_limits(permission_manager, test_users)
        
        # 5. 测试使用监控功能
        test_usage_monitoring()
        
        # 6. 显示测试总结
        display_summary()
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("测试结束")


if __name__ == "__main__":
    main()
