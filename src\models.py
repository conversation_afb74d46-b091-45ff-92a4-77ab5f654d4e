"""
数据模型定义
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass
class TempMailConfig:
    """临时邮箱配置"""
    username: str
    email_extension: str
    epin: str


@dataclass
class AppConfig:
    """应用配置"""
    temp_mail: TempMailConfig
    email_domain: str = "@465447.xyz"
    max_retries: int = 5
    retry_interval: int = 3000


@dataclass
class EmailInfo:
    """邮箱信息"""
    address: str
    generated_time: datetime


@dataclass
class MailMessage:
    """邮件消息"""
    id: str
    subject: str
    content: str
    received_time: datetime
    verification_code: Optional[str] = None


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: datetime
    level: str  # INFO, WARNING, ERROR, SUCCESS
    message: str