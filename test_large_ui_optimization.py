#!/usr/bin/env python3
"""
大尺寸界面优化测试工具
验证第三轮字体和界面优化效果
"""
import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from src.gui.theme_manager import theme_manager


class LargeUITestWindow:
    """大尺寸界面测试窗口"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("第三轮大尺寸界面优化测试")
        self.root.geometry("1400x1000")  # 使用更大的测试窗口
        
        # 应用主题
        self.style = theme_manager.apply_theme(self.root)
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建测试界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)
        
        # 标题
        title_label = ttk.Label(main_frame, text="第三轮大尺寸界面优化测试", style='Title.TLabel')
        title_label.pack(pady=(0, 30))
        
        # 系统信息
        info_frame = ttk.LabelFrame(main_frame, text="系统信息")
        info_frame.pack(fill=tk.X, pady=(0, 30))
        
        system_info = f"操作系统: {theme_manager.system}\n"
        system_info += f"DPI缩放: {theme_manager.dpi_scale:.2f}\n"
        system_info += f"系统字体: {theme_manager.system_font}\n"
        system_info += f"等宽字体: {theme_manager.monospace_font}\n"
        system_info += f"窗口大小: 1400x1000 (测试用)"
        
        info_label = ttk.Label(info_frame, text=system_info, justify=tk.LEFT)
        info_label.pack(padx=20, pady=15, anchor='w')
        
        # 创建三列对比框架
        comparison_frame = ttk.Frame(main_frame)
        comparison_frame.pack(fill=tk.BOTH, expand=True)
        
        # 第一轮优化
        first_frame = ttk.LabelFrame(comparison_frame, text="第一轮优化 (原始)")
        first_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # 第二轮优化
        second_frame = ttk.LabelFrame(comparison_frame, text="第二轮优化")
        second_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 10))
        
        # 第三轮优化（当前）
        third_frame = ttk.LabelFrame(comparison_frame, text="第三轮优化 (当前)")
        third_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # 字体大小对比数据
        font_comparisons = [
            ("标题字体", "这是标题字体", 18, 22, 28),
            ("副标题字体", "这是副标题字体", 14, 16, 20),
            ("正文字体", "这是正文字体测试", 11, 13, 16),
            ("小字体", "这是小字体", 10, 12, 14),
            ("按钮字体", "按钮文字", 11, 13, 16),
            ("验证码字体", "123456", 16, 18, 22),
        ]
        
        # 创建对比内容
        for i, (name, text, first_size, second_size, third_size) in enumerate(font_comparisons):
            # 第一轮优化
            first_test_frame = ttk.Frame(first_frame)
            first_test_frame.pack(fill=tk.X, padx=10, pady=8)
            
            first_name_label = ttk.Label(first_test_frame, text=f"{name}:", width=12, anchor='w')
            first_name_label.pack(side=tk.LEFT, padx=(0, 10))
            
            first_font = (theme_manager.system_font, first_size, 'bold' if 'title' in name.lower() else 'normal')
            first_sample_label = ttk.Label(first_test_frame, text=text, font=first_font)
            first_sample_label.pack(side=tk.LEFT, anchor='w')
            
            # 第二轮优化
            second_test_frame = ttk.Frame(second_frame)
            second_test_frame.pack(fill=tk.X, padx=10, pady=8)
            
            second_name_label = ttk.Label(second_test_frame, text=f"{name}:", width=12, anchor='w')
            second_name_label.pack(side=tk.LEFT, padx=(0, 10))
            
            second_font = (theme_manager.system_font, second_size, 'bold' if 'title' in name.lower() else 'normal')
            second_sample_label = ttk.Label(second_test_frame, text=text, font=second_font)
            second_sample_label.pack(side=tk.LEFT, anchor='w')
            
            # 第三轮优化（当前主题）
            third_test_frame = ttk.Frame(third_frame)
            third_test_frame.pack(fill=tk.X, padx=10, pady=8)
            
            third_name_label = ttk.Label(third_test_frame, text=f"{name}:", width=12, anchor='w')
            third_name_label.pack(side=tk.LEFT, padx=(0, 10))
            
            # 使用当前主题的字体
            if 'title' in name.lower():
                style_name = 'Title.TLabel'
            elif 'subtitle' in name.lower():
                style_name = 'Subtitle.TLabel'
            elif 'small' in name.lower():
                style_name = 'Small.TLabel'
            elif 'verification' in name.lower():
                style_name = 'VerificationCode.TLabel'
            else:
                style_name = 'TLabel'
            
            third_sample_label = ttk.Label(third_test_frame, text=text, style=style_name)
            third_sample_label.pack(side=tk.LEFT, anchor='w')
        
        # 字体大小改进统计
        stats_frame = ttk.LabelFrame(main_frame, text="第三轮优化改进统计")
        stats_frame.pack(fill=tk.X, pady=(30, 0))
        
        stats_text = "字体大小改进历程:\n\n"
        stats_text += "第一轮 → 第二轮 → 第三轮优化:\n"
        stats_text += "• 标题字体: 18px → 22px → 28px (总提升 +56%)\n"
        stats_text += "• 副标题字体: 14px → 16px → 20px (总提升 +43%)\n"
        stats_text += "• 正文字体: 11px → 13px → 16px (总提升 +45%)\n"
        stats_text += "• 小字体: 10px → 12px → 14px (总提升 +40%)\n"
        stats_text += "• 按钮字体: 11px → 13px → 16px (总提升 +45%)\n"
        stats_text += "• 验证码字体: 16px → 18px → 22px (总提升 +38%)\n\n"
        
        stats_text += "界面尺寸改进:\n"
        stats_text += "• 主窗口: 800x600 → 1200x900 (+50%)\n"
        stats_text += "• 最小尺寸: 600x450 → 1000x750 (+67%)\n"
        stats_text += "• 对话框: 500x400 → 700x550 (+40%)\n"
        stats_text += "• 边距间距: 全面增加 40-70%\n\n"
        
        stats_text += f"DPI缩放系数: {theme_manager.dpi_scale:.2f}x\n"
        stats_text += "最小字体限制: 已大幅提升至 13-26px"
        
        stats_label = ttk.Label(stats_frame, text=stats_text, justify=tk.LEFT)
        stats_label.pack(padx=20, pady=15, anchor='w')
        
        # 控件测试区域
        widget_frame = ttk.LabelFrame(main_frame, text="大尺寸控件测试")
        widget_frame.pack(fill=tk.X, pady=(20, 0))
        
        # 按钮测试
        button_frame = ttk.Frame(widget_frame)
        button_frame.pack(fill=tk.X, padx=15, pady=10)
        
        ttk.Label(button_frame, text="按钮测试:", width=15, anchor='w').pack(side=tk.LEFT)
        
        ttk.Button(button_frame, text="主要按钮").pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="次要按钮", style='Secondary.TButton').pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="成功按钮", style='Success.TButton').pack(side=tk.LEFT, padx=5)
        
        # 输入框测试
        entry_frame = ttk.Frame(widget_frame)
        entry_frame.pack(fill=tk.X, padx=15, pady=10)
        
        ttk.Label(entry_frame, text="输入框测试:", width=15, anchor='w').pack(side=tk.LEFT)
        
        entry = ttk.Entry(entry_frame, width=30)
        entry.pack(side=tk.LEFT, padx=5)
        entry.insert(0, "大字体输入框测试")
        
        # 下拉框测试
        combo_frame = ttk.Frame(widget_frame)
        combo_frame.pack(fill=tk.X, padx=15, pady=10)
        
        ttk.Label(combo_frame, text="下拉框测试:", width=15, anchor='w').pack(side=tk.LEFT)
        
        combo = ttk.Combobox(combo_frame, values=["选项1", "选项2", "选项3"], width=27)
        combo.pack(side=tk.LEFT, padx=5)
        combo.set("大字体下拉框测试")
        
        # 选项卡测试
        notebook_frame = ttk.Frame(widget_frame)
        notebook_frame.pack(fill=tk.X, padx=15, pady=10)
        
        ttk.Label(notebook_frame, text="选项卡测试:", width=15, anchor='w').pack(side=tk.LEFT)
        
        test_notebook = ttk.Notebook(notebook_frame)
        test_notebook.pack(side=tk.LEFT, padx=5)
        
        tab1 = ttk.Frame(test_notebook)
        tab2 = ttk.Frame(test_notebook)
        test_notebook.add(tab1, text="选项卡1")
        test_notebook.add(tab2, text="选项卡2")
        
        ttk.Label(tab1, text="大字体选项卡内容").pack(padx=20, pady=20)
        ttk.Label(tab2, text="第二个选项卡").pack(padx=20, pady=20)
        
        # 底部说明
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=(20, 0))
        
        explanation = ("第三轮大尺寸界面优化说明:\n"
                      "1. 字体大小全面提升 40-56%，确保在任何分辨率下都清晰易读\n"
                      "2. 窗口尺寸显著增大，提供更宽敞的操作空间\n"
                      "3. 界面间距全面优化，适应更大的字体和控件\n"
                      "4. 保持了良好的视觉层次和用户体验")
        
        ttk.Label(bottom_frame, text=explanation, style='Small.TLabel', 
                 wraplength=1200, justify=tk.LEFT).pack(anchor='w')
    
    def run(self):
        """运行测试窗口"""
        self.root.mainloop()


def main():
    """主函数"""
    print("启动第三轮大尺寸界面优化测试...")
    print("=" * 70)
    print("测试内容:")
    print("• 第三轮字体大小优化效果")
    print("• 大尺寸窗口界面效果")
    print("• 界面元素间距优化")
    print("• 各种控件的显示效果")
    print("• 三轮优化的对比展示")
    print("=" * 70)
    
    try:
        test_window = LargeUITestWindow()
        test_window.run()
    except Exception as e:
        print(f"测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
