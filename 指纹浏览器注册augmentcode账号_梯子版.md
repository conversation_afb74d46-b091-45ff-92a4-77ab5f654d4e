## 1 安装指纹浏览器

### 1.1 双击AdsPower-Global-7.7.18-x64.exe 进行软件安装

![1753946009143](image/指纹浏览器注册augmentcode账号_梯子版/1753946009143.png)

### 1.2 打开指纹浏览器，点击“新建浏览器”

![1753946110850](image/指纹浏览器注册augmentcode账号_梯子版/1753946110850.png)

### 1.3 进行浏览器配置

![1753946373952](image/指纹浏览器注册augmentcode账号_梯子版/1753946373952.png)

![1753946465225](image/指纹浏览器注册augmentcode账号_梯子版/1753946465225.png)

**主机-端口如何获取呢？？？**

打开电脑  控制面板 -> 网络和Internet -> Internet选项 -> Internet属性 - 连接 -> 局域网设置

![1753946686927](image/指纹浏览器注册augmentcode账号_梯子版/1753946686927.png)

![1753946782241](image/指纹浏览器注册augmentcode账号_梯子版/1753946782241.png)

将以上地址和端口填入 指纹浏览器 的 主机-端口 处。

![1753946873527](image/指纹浏览器注册augmentcode账号_梯子版/1753946873527.png)

点击  生成指纹 -->  确定。

## 2 打开梯子，选择节点（最好是美国节点）

## 3 浏览器注册augmentcode 账号

### 3.1 打开刚才已注册好的浏览，打开后首先确定当前IP 是否  “非国内”

![1753947113206](image/指纹浏览器注册augmentcode账号_梯子版/1753947113206.png)

![1753947429664](image/指纹浏览器注册augmentcode账号_梯子版/1753947429664.png)

### 3.2  确认OK 后，打开augmentcode 官网，进行注册流程

![1753947517020](image/指纹浏览器注册augmentcode账号_梯子版/1753947517020.png)

![1753947686419](image/指纹浏览器注册augmentcode账号_梯子版/1753947686419.png)

![1753948443153](image/指纹浏览器注册augmentcode账号_梯子版/1753948443153.png)

![1753948478233](image/指纹浏览器注册augmentcode账号_梯子版/1753948478233.png)

![1753948541720](image/指纹浏览器注册augmentcode账号_梯子版/1753948541720.png)

**到此，账号注册成功！！！**

## 4 VScode 登录augment 账号

### 4.1 点击augment 插件 sign in

![1753948692897](image/指纹浏览器注册augmentcode账号_梯子版/1753948692897.png)

### 4.2 输入刚注册的邮箱 和验证码后，会跳出图示弹框，点击跳转到VScode

![1753948856014](image/指纹浏览器注册augmentcode账号_梯子版/1753948856014.png)

### 4.3 等待 augment 插件登录刷新成功后，即可使用。可以看到是claude4.0 大模型

![1753948980754](image/指纹浏览器注册augmentcode账号_梯子版/1753948980754.png)

## 5 augmentcode自动注册助手介绍（AugmentCode.exe）
