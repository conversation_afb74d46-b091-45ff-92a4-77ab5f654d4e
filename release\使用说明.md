# AugmentCode自动注册助手 - 简化版使用指导

## 📖 概述

AugmentCode自动注册助手简化版是一个轻量级的临时邮箱生成工具，专为快速生成临时邮箱地址和获取验证码而设计。

## 🚀 快速开始

### 启动应用
```bash
python run_simple_app.py
```

### 首次使用
1. 启动应用后会自动弹出用户认证对话框
2. 选择合适的用户类型登录
3. 开始使用邮箱生成功能

## 👤 用户类型说明

### 体验用户 (trial)
- **邮箱生成限制**: 总共1次
- **验证码获取**: 无限制
- **操作间隔**: 30秒
- **适用场景**: 初次体验用户

### 月度会员 (monthly_user)
- **邮箱生成限制**: 总共4次
- **验证码获取**: 无限制
- **操作间隔**: 10秒
- **适用场景**: 轻度使用用户

### VIP会员 (super_user)
- **邮箱生成限制**: 无限制
- **验证码获取**: 无限制
- **操作间隔**: 5秒
- **适用场景**: 重度使用用户

## 🔐 用户认证

### 登录步骤
1. 在认证对话框中选择"用户登录"标签页
2. 从预设用户中选择一个账号类型：
   - 点击"体验用户"、"月度会员"或"VIP会员"按钮
3. 或者手动输入用户名
4. 点击"登录"按钮

### 预设账号
- `trial` - 体验用户
- `monthly_user` - 月度会员
- `super_user` - VIP会员

## 📧 邮箱生成功能

### 生成邮箱
1. **设置生成数量**
   - 在"生成数量"输入框中输入1-50之间的数字
   - 默认为1个邮箱

2. **点击生成按钮**
   - 点击"生成邮箱"按钮
   - 系统会自动生成指定数量的临时邮箱

3. **查看生成结果**
   - 生成的邮箱会显示在下方的列表中
   - 包含序号、邮箱地址和生成时间

### 邮箱格式
生成的邮箱格式为：`{随机用户名}@465447.xyz`

示例：`<EMAIL>`

## 📋 邮箱管理

### 查看邮箱列表
- 所有生成的邮箱都会显示在主界面的表格中
- 显示信息包括：序号、邮箱地址、生成时间
- 底部显示邮箱总数统计

### 复制邮箱地址
- **双击邮箱地址** - 自动复制到剪贴板
- **右键菜单** - 选择"复制邮箱"选项
- 复制成功后状态栏会显示确认信息

### 删除邮箱
- **右键菜单** - 选择"删除选中"
- **确认删除** - 在弹出的确认对话框中点击"是"

### 清空所有邮箱
- 点击"清除列表"按钮
- 在确认对话框中点击"是"
- 所有邮箱将被清除

## 🔍 验证码获取

### 获取验证码
1. **选择邮箱**
   - 在邮箱列表中选择要获取验证码的邮箱
   - 双击或右键选择

2. **点击获取按钮**
   - 点击"验证码获取"按钮
   - 系统会自动检查该邮箱的新邮件

3. **查看验证码**
   - 如果有验证码邮件，会自动提取并显示
   - 验证码会显示在弹出窗口中

## 💾 数据持久化

### 自动保存
- **邮箱数据自动保存** - 每次生成邮箱后自动保存到本地
- **按用户隔离** - 不同用户的邮箱数据分别存储
- **重启后恢复** - 重新登录后自动加载历史邮箱

### 存储位置
- 邮箱数据存储在：`data/emails/{用户ID}_emails.json`
- 用户数据存储在：`data/users/users.json`

### 数据管理
- **登录时加载** - 用户登录后自动加载历史邮箱
- **登出时清空** - 用户登出后界面清空（但文件保留）
- **清除时删除** - 点击"清除列表"会同时删除存储文件

## ⚙️ 界面操作

### 主界面布局
```
┌─────────────────────────────────────┐
│ 菜单栏: 用户 | 帮助                    │
├─────────────────────────────────────┤
│ 邮箱生成控制区                        │
│ [生成数量: 1] [生成邮箱] [清除列表]     │
├─────────────────────────────────────┤
│ 生成的邮箱地址列表                    │
│ ┌─────┬──────────────┬────────────┐ │
│ │序号 │ 邮箱地址      │ 生成时间    │ │
│ ├─────┼──────────────┼────────────┤ │
│ │ 1   │ <EMAIL> │ 2025-01-01 │ │
│ └─────┴──────────────┴────────────┘ │
├─────────────────────────────────────┤
│ 验证码获取区                         │
│ [验证码获取]                         │
├─────────────────────────────────────┤
│ 状态栏: 当前状态信息                  │
└─────────────────────────────────────┘
```

### 菜单功能
- **用户菜单**
  - 查看用户信息
  - 权限说明
  - 退出登录

- **帮助菜单**
  - 使用说明
  - 关于软件

### 状态栏信息
- 显示当前登录用户和权限级别
- 显示操作结果和状态信息
- 显示错误提示和成功确认

## ⚠️ 使用限制

### 权限限制
- **体验用户**: 总共只能生成1个邮箱
- **月度会员**: 总共只能生成4个邮箱
- **VIP会员**: 无邮箱生成限制

### 操作限制
- **频率限制**: 根据用户类型有不同的操作间隔
- **数量限制**: 单次最多生成50个邮箱
- **网络依赖**: 需要网络连接获取验证码

### 错误处理
- **权限不足**: 会弹出提示对话框说明限制
- **网络错误**: 会显示网络连接失败信息
- **操作过快**: 会提示等待操作间隔

## 🔧 故障排除

### 常见问题

**Q: 无法生成邮箱？**
A: 检查是否达到用户权限限制，或等待操作间隔时间

**Q: 验证码获取失败？**
A: 确保网络连接正常，稍后重试

**Q: 邮箱数据丢失？**
A: 检查`data/emails/`目录是否存在，重新登录尝试加载

**Q: 登录失败？**
A: 确认用户名正确，或选择预设用户按钮

### 日志查看
- 控制台会显示详细的操作日志
- 包括邮箱生成、保存、加载等信息
- 错误信息也会在控制台显示

## 💡 使用技巧

### 高效使用建议
1. **批量生成**: 一次性生成多个邮箱，避免频繁操作
2. **及时复制**: 生成后立即复制需要的邮箱地址
3. **定期清理**: 不需要的邮箱及时清除，保持列表整洁
4. **权限规划**: 根据使用需求选择合适的用户类型

### 快捷操作
- **Ctrl+C**: 复制选中的邮箱地址
- **双击**: 快速复制邮箱到剪贴板
- **右键**: 显示上下文菜单
- **Delete键**: 删除选中的邮箱

### 最佳实践
1. **邮箱命名**: 系统自动生成，无需手动设置
2. **数据备份**: 重要邮箱地址建议额外保存
3. **及时获取**: 验证码有时效性，及时获取和使用
4. **网络稳定**: 确保网络连接稳定以获得最佳体验

## 🔒 安全说明

### 数据安全
- **本地存储**: 所有数据存储在本地，不上传到服务器
- **临时邮箱**: 生成的邮箱为临时性质，请勿用于重要账号
- **隐私保护**: 软件不收集用户个人信息

### 使用建议
- **测试用途**: 建议仅用于测试和临时注册
- **重要账号**: 重要账号请使用正式邮箱
- **定期清理**: 定期清理不需要的临时邮箱

## 📊 功能对比

| 功能 | 体验用户 | 月度会员 | VIP会员 |
|------|----------|----------|---------|
| 邮箱生成 | 1次 | 4次 | 无限制 |
| 验证码获取 | 无限制 | 无限制 | 无限制 |
| 操作间隔 | 30秒 | 10秒 | 5秒 |
| 数据保存 | ✅ | ✅ | ✅ |
| 批量生成 | ✅ | ✅ | ✅ |
| 邮箱管理 | ✅ | ✅ | ✅ |

## 📞 技术支持

### 问题反馈
如遇到问题，请：
1. 查看控制台错误信息
2. 检查网络连接状态
3. 重启应用程序尝试
4. 查看日志文件
5. 联系技术支持

### 联系方式
- **GitHub**: [项目地址]
- **邮箱**: <EMAIL>
- **文档**: 查看完整文档获取更多帮助

---

**版本**: 简化版 v1.0
**更新日期**: 2025-07-19
**开发者**: AugmentCode Team
**许可证**: MIT License
