#!/usr/bin/env python3
"""
预设用户系统测试工具
验证体验123、basic_user、vip_user三种预设用户的权限配置
"""
import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from src.user_management.user_permission_system import UserPermissionManager, UserRole


class PresetUserTestWindow:
    """预设用户测试窗口"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("预设用户权限测试")
        self.root.geometry("1000x700")
        
        # 初始化权限管理器
        self.permission_manager = UserPermissionManager()
        
        self._create_widgets()
        self._test_preset_users()
    
    def _create_widgets(self):
        """创建测试界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="预设用户权限测试", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 测试结果显示区域
        self.result_text = tk.Text(main_frame, height=35, width=120, 
                                  font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, 
                                 command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 刷新按钮
        refresh_btn = ttk.Button(main_frame, text="重新测试", 
                               command=self._test_preset_users)
        refresh_btn.pack(pady=(10, 0))
    
    def _log(self, message):
        """记录测试日志"""
        self.result_text.insert(tk.END, message + "\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def _test_preset_users(self):
        """测试预设用户"""
        self.result_text.delete(1.0, tk.END)
        
        self._log("=" * 80)
        self._log("预设用户权限测试")
        self._log("=" * 80)
        
        # 测试用户配置
        test_users = [
            {
                "username": "体验123",
                "expected_role": UserRole.TRIAL,
                "description": "体验用户",
                "expected_email_limit": 1,
                "expected_verification_limit": -1
            },
            {
                "username": "basic_user", 
                "expected_role": UserRole.BASIC,
                "description": "月度会员",
                "expected_email_limit": 4,
                "expected_verification_limit": -1
            },
            {
                "username": "vip_user",
                "expected_role": UserRole.VIP,
                "description": "VIP会员", 
                "expected_email_limit": -1,
                "expected_verification_limit": -1
            }
        ]
        
        for i, user_config in enumerate(test_users, 1):
            self._log(f"\n{i}. 测试用户: {user_config['username']} ({user_config['description']})")
            self._log("-" * 60)
            
            # 测试用户登录
            user_info = self.permission_manager.login_user(user_config["username"])
            if user_info:
                self._log(f"✅ 用户登录成功")
                self._log(f"   用户名: {user_info.username}")
                self._log(f"   角色: {user_info.role.value}")
                self._log(f"   备注: {user_info.notes}")
                
                # 验证角色
                if user_info.role == user_config["expected_role"]:
                    self._log(f"✅ 角色验证通过: {user_info.role.value}")
                else:
                    self._log(f"❌ 角色验证失败: 期望 {user_config['expected_role'].value}, 实际 {user_info.role.value}")
                
                # 测试邮箱生成权限
                email_allowed, email_reason, email_used, email_limit = self.permission_manager.check_email_limit()
                self._log(f"📧 邮箱生成权限:")
                self._log(f"   是否允许: {'✅ 是' if email_allowed else '❌ 否'}")
                self._log(f"   限制数量: {'无限制' if email_limit == -1 else f'{email_limit}次/日'}")
                self._log(f"   已使用: {email_used}次")
                self._log(f"   状态说明: {email_reason}")
                
                # 验证邮箱限制
                if email_limit == user_config["expected_email_limit"]:
                    self._log(f"✅ 邮箱限制验证通过")
                else:
                    self._log(f"❌ 邮箱限制验证失败: 期望 {user_config['expected_email_limit']}, 实际 {email_limit}")
                
                # 测试验证码获取权限
                verify_allowed, verify_reason, verify_used, verify_limit = self.permission_manager.check_verification_limit()
                self._log(f"🔐 验证码获取权限:")
                self._log(f"   是否允许: {'✅ 是' if verify_allowed else '❌ 否'}")
                self._log(f"   限制数量: {'无限制' if verify_limit == -1 else f'{verify_limit}次/日'}")
                self._log(f"   已使用: {verify_used}次")
                self._log(f"   状态说明: {verify_reason}")
                
                # 验证验证码限制
                if verify_limit == user_config["expected_verification_limit"]:
                    self._log(f"✅ 验证码限制验证通过")
                else:
                    self._log(f"❌ 验证码限制验证失败: 期望 {user_config['expected_verification_limit']}, 实际 {verify_limit}")
                
                # 测试频率限制
                rate_allowed, rate_reason, wait_time = self.permission_manager.check_rate_limit()
                self._log(f"⏱️ 操作频率限制:")
                self._log(f"   是否允许: {'✅ 是' if rate_allowed else '❌ 否'}")
                self._log(f"   状态说明: {rate_reason}")
                if wait_time > 0:
                    self._log(f"   等待时间: {wait_time}秒")
                
                # 登出用户
                self.permission_manager.logout_user()
                self._log(f"🚪 用户已登出")
                
            else:
                self._log(f"❌ 用户登录失败: 用户不存在")
        
        # 总结
        self._log("\n" + "=" * 80)
        self._log("测试总结")
        self._log("=" * 80)
        self._log("预设用户权限配置:")
        self._log("• 体验123 (体验用户): 1次邮箱生成，不限验证码")
        self._log("• basic_user (月度会员): 4次邮箱生成，不限验证码")
        self._log("• vip_user (VIP会员): 不限邮箱生成，不限验证码")
        self._log("")
        self._log("所有预设用户已创建并可正常使用！")
        self._log("用户可以通过用户认证对话框的快速登录按钮直接登录。")
    
    def run(self):
        """运行测试窗口"""
        self.root.mainloop()


def main():
    """主函数"""
    print("启动预设用户权限测试...")
    print("=" * 60)
    print("测试内容:")
    print("• 体验123 - 体验用户权限测试")
    print("• basic_user - 月度会员权限测试")
    print("• vip_user - VIP会员权限测试")
    print("=" * 60)
    
    try:
        test_window = PresetUserTestWindow()
        test_window.run()
    except Exception as e:
        print(f"测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
