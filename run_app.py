'''
Author: yangyang <EMAIL>
Date: 2025-07-17 18:12:30
LastEditors: yangyang
LastEditTime: 2025-07-18 11:33:03
FilePath: \Augment\run_app.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
"""
启动应用程序
"""
import sys
import os
import traceback
from tkinter import messagebox

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入应用程序
try:
    from src.app import Application
    
    # 运行应用程序
    if __name__ == "__main__":
        try:
            app = Application()
            app.run()
        except Exception as e:
            # 捕获未处理的异常
            error_msg = f"应用程序发生未处理的异常:\n{str(e)}\n\n{traceback.format_exc()}"
            print(error_msg)
            
            try:
                messagebox.showerror("严重错误", error_msg)
            except:
                pass
            
            sys.exit(1)
except Exception as e:
    print(f"导入错误: {str(e)}")
    print(traceback.format_exc())
    input("按Enter键退出...")