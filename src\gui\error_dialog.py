"""
自定义错误对话框
"""
import tkinter as tk
from tkinter import ttk
from src.gui.theme_manager import theme_manager
from src.gui.custom_widgets import ModernButton


class ErrorDialog:
    """自定义错误对话框，提供更友好的错误消息显示"""
    
    @staticmethod
    def show_error(parent, title, message, suggestion=None, details=None):
        """显示错误对话框
        
        Args:
            parent: 父窗口
            title: 错误标题
            message: 错误消息
            suggestion: 解决建议（可选）
            details: 详细错误信息（可选）
        """
        # 创建对话框
        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.geometry("450x300")
        dialog.minsize(400, 200)
        dialog.transient(parent)  # 设置为父窗口的子窗口
        dialog.grab_set()  # 模态对话框
        
        # 应用主题
        dialog.configure(bg=theme_manager.colors['bg_light'])
        
        # 居中显示
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")
        
        # 创建内容框架
        content_frame = ttk.Frame(dialog)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 创建错误图标和标题区域
        header_frame = ttk.Frame(content_frame)
        header_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 错误图标（使用文本代替，可以替换为实际图标）
        icon_label = ttk.Label(header_frame, text="❌", 
                              font=(theme_manager.system_font, 24),
                              foreground=theme_manager.colors['danger'])
        icon_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # 错误标题
        title_label = ttk.Label(header_frame, text=title, 
                               style='Title.TLabel',
                               foreground=theme_manager.colors['danger'])
        title_label.pack(side=tk.LEFT)
        
        # 错误消息
        message_frame = ttk.Frame(content_frame)
        message_frame.pack(fill=tk.BOTH, expand=True)
        
        message_label = ttk.Label(message_frame, text=message, 
                                 wraplength=400, justify=tk.LEFT)
        message_label.pack(anchor='w', pady=(0, 10))
        
        # 解决建议（如果有）
        if suggestion:
            suggestion_frame = ttk.Frame(content_frame, 
                                       style='Card.TFrame')
            suggestion_frame.pack(fill=tk.X, pady=(0, 15))
            
            suggestion_title = ttk.Label(suggestion_frame, text="解决建议:", 
                                       font=(theme_manager.system_font, 10, 'bold'))
            suggestion_title.pack(anchor='w', padx=10, pady=(10, 5))
            
            suggestion_label = ttk.Label(suggestion_frame, text=suggestion, 
                                       wraplength=380, justify=tk.LEFT)
            suggestion_label.pack(anchor='w', padx=10, pady=(0, 10))
        
        # 详细信息（如果有）
        if details:
            details_var = tk.BooleanVar(value=False)
            details_frame = ttk.Frame(content_frame)
            details_frame.pack(fill=tk.X)
            
            details_button = ttk.Checkbutton(details_frame, text="显示详细信息", 
                                           variable=details_var,
                                           command=lambda: toggle_details())
            details_button.pack(anchor='w')
            
            details_text = tk.Text(content_frame, height=5, width=50, 
                                  font=(theme_manager.monospace_font, 9),
                                  wrap=tk.WORD)
            details_text.insert(tk.END, details)
            details_text.config(state=tk.DISABLED)
            
            def toggle_details():
                if details_var.get():
                    details_text.pack(fill=tk.X, pady=(5, 10))
                    dialog.geometry("450x400")
                else:
                    details_text.pack_forget()
                    dialog.geometry("450x300")
        
        # 按钮区域
        button_frame = ttk.Frame(content_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ok_button = ModernButton(button_frame, text="确定", 
                               command=dialog.destroy)
        ok_button.pack(side=tk.RIGHT)
        
        # 等待对话框关闭
        dialog.wait_window()


class WarningDialog(ErrorDialog):
    """警告对话框"""
    
    @staticmethod
    def show_warning(parent, title, message, suggestion=None, details=None):
        """显示警告对话框"""
        # 创建对话框
        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.geometry("450x300")
        dialog.minsize(400, 200)
        dialog.transient(parent)  # 设置为父窗口的子窗口
        dialog.grab_set()  # 模态对话框
        
        # 应用主题
        dialog.configure(bg=theme_manager.colors['bg_light'])
        
        # 居中显示
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")
        
        # 创建内容框架
        content_frame = ttk.Frame(dialog)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 创建警告图标和标题区域
        header_frame = ttk.Frame(content_frame)
        header_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 警告图标（使用文本代替，可以替换为实际图标）
        icon_label = ttk.Label(header_frame, text="⚠️", 
                              font=(theme_manager.system_font, 24),
                              foreground=theme_manager.colors['warning'])
        icon_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # 警告标题
        title_label = ttk.Label(header_frame, text=title, 
                               style='Title.TLabel',
                               foreground=theme_manager.colors['warning'])
        title_label.pack(side=tk.LEFT)
        
        # 警告消息
        message_frame = ttk.Frame(content_frame)
        message_frame.pack(fill=tk.BOTH, expand=True)
        
        message_label = ttk.Label(message_frame, text=message, 
                                 wraplength=400, justify=tk.LEFT)
        message_label.pack(anchor='w', pady=(0, 10))
        
        # 解决建议（如果有）
        if suggestion:
            suggestion_frame = ttk.Frame(content_frame, 
                                       style='Card.TFrame')
            suggestion_frame.pack(fill=tk.X, pady=(0, 15))
            
            suggestion_title = ttk.Label(suggestion_frame, text="建议:", 
                                       font=(theme_manager.system_font, 10, 'bold'))
            suggestion_title.pack(anchor='w', padx=10, pady=(10, 5))
            
            suggestion_label = ttk.Label(suggestion_frame, text=suggestion, 
                                       wraplength=380, justify=tk.LEFT)
            suggestion_label.pack(anchor='w', padx=10, pady=(0, 10))
        
        # 详细信息（如果有）
        if details:
            details_var = tk.BooleanVar(value=False)
            details_frame = ttk.Frame(content_frame)
            details_frame.pack(fill=tk.X)
            
            details_button = ttk.Checkbutton(details_frame, text="显示详细信息", 
                                           variable=details_var,
                                           command=lambda: toggle_details())
            details_button.pack(anchor='w')
            
            details_text = tk.Text(content_frame, height=5, width=50, 
                                  font=(theme_manager.monospace_font, 9),
                                  wrap=tk.WORD)
            details_text.insert(tk.END, details)
            details_text.config(state=tk.DISABLED)
            
            def toggle_details():
                if details_var.get():
                    details_text.pack(fill=tk.X, pady=(5, 10))
                    dialog.geometry("450x400")
                else:
                    details_text.pack_forget()
                    dialog.geometry("450x300")
        
        # 按钮区域
        button_frame = ttk.Frame(content_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ok_button = ModernButton(button_frame, text="确定", 
                               command=dialog.destroy)
        ok_button.pack(side=tk.RIGHT)
        
        # 等待对话框关闭
        dialog.wait_window()