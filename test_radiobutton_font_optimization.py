#!/usr/bin/env python3
"""
单选按钮字体优化测试工具
验证用户角色选择区域的字体大小优化效果
"""
import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from src.gui.theme_manager import theme_manager
from src.user_management.user_permission_system import UserRole


class RadioButtonFontTestWindow:
    """单选按钮字体测试窗口"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("单选按钮字体优化测试")
        self.root.geometry("1000x700")
        
        # 应用主题
        self.style = theme_manager.apply_theme(self.root)
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建测试界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # 标题
        title_label = ttk.Label(main_frame, text="单选按钮字体优化测试", style='Title.TLabel')
        title_label.pack(pady=(0, 30))
        
        # 优化说明
        info_frame = ttk.LabelFrame(main_frame, text="字体优化内容")
        info_frame.pack(fill=tk.X, pady=(0, 30))
        
        optimization_info = """单选按钮字体优化:
• 标签字体: TLabel → Subtitle.TLabel (16px → 20px, +25%)
• 单选按钮字体: TRadiobutton → Large.TRadiobutton (16px → 20px, +25%)
• 间距优化: 4px → 6px (+50%)
• 标签间距: (0,8) → (0,10) (+25%)

显示效果:
• 文字更大更清晰，易于阅读
• 选项之间间距更合理
• 整体视觉效果更专业"""
        
        info_label = ttk.Label(info_frame, text=optimization_info, 
                              justify=tk.LEFT, style='TLabel')
        info_label.pack(padx=20, pady=15, anchor='w')
        
        # 对比测试区域
        comparison_frame = ttk.Frame(main_frame)
        comparison_frame.pack(fill=tk.BOTH, expand=True)
        
        # 原始字体效果
        original_frame = ttk.LabelFrame(comparison_frame, text="原始字体效果 (16px)")
        original_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 15))
        
        # 优化后字体效果
        optimized_frame = ttk.LabelFrame(comparison_frame, text="优化后字体效果 (20px)")
        optimized_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(15, 0))
        
        # 创建原始字体的单选按钮组
        self._create_original_radiobuttons(original_frame)
        
        # 创建优化后字体的单选按钮组
        self._create_optimized_radiobuttons(optimized_frame)
        
        # 字体对比信息
        font_info_frame = ttk.LabelFrame(main_frame, text="字体大小对比")
        font_info_frame.pack(fill=tk.X, pady=(30, 0))
        
        font_comparison = f"""字体配置对比:

原始配置:
• 标签字体: TLabel - {theme_manager.fonts['body']} (16px)
• 单选按钮: TRadiobutton - {theme_manager.fonts['body']} (16px)
• 间距: 4px

优化后配置:
• 标签字体: Subtitle.TLabel - {theme_manager.fonts['subtitle']} (20px)
• 单选按钮: Large.TRadiobutton - {theme_manager.fonts['subtitle']} (20px)
• 间距: 6px

改进效果:
✅ 字体大小提升25% (16px → 20px)
✅ 间距增加50% (4px → 6px)
✅ 文字清晰度显著提升
✅ 用户体验更加友好"""
        
        font_info_label = ttk.Label(font_info_frame, text=font_comparison, 
                                   justify=tk.LEFT, style='Small.TLabel')
        font_info_label.pack(padx=20, pady=15, anchor='w')
    
    def _create_original_radiobuttons(self, parent):
        """创建原始字体的单选按钮组"""
        # 标签
        ttk.Label(parent, text="用户角色:", style='TLabel').pack(anchor='w', pady=(20, 8))
        
        # 单选按钮变量
        self.original_var = tk.StringVar(value=UserRole.BASIC.value)
        
        roles = [
            (UserRole.BASIC, "基础用户 (每日5个邮箱)"),
            (UserRole.PREMIUM, "高级用户 (每日20个邮箱)"),
            (UserRole.VIP, "VIP用户 (每日100个邮箱)")
        ]
        
        for role, description in roles:
            # 使用原始字体样式
            radio_btn = ttk.Radiobutton(parent, text=description, 
                                       variable=self.original_var, value=role.value,
                                       style='TRadiobutton')  # 原始样式 (16px)
            radio_btn.pack(anchor='w', pady=4)  # 原始间距
    
    def _create_optimized_radiobuttons(self, parent):
        """创建优化后字体的单选按钮组"""
        # 标签 - 使用副标题字体
        ttk.Label(parent, text="用户角色:", style='Subtitle.TLabel').pack(anchor='w', pady=(20, 10))
        
        # 单选按钮变量
        self.optimized_var = tk.StringVar(value=UserRole.BASIC.value)
        
        roles = [
            (UserRole.BASIC, "基础用户 (每日5个邮箱)"),
            (UserRole.PREMIUM, "高级用户 (每日20个邮箱)"),
            (UserRole.VIP, "VIP用户 (每日100个邮箱)")
        ]
        
        for role, description in roles:
            # 使用优化后的大字体样式
            radio_btn = ttk.Radiobutton(parent, text=description, 
                                       variable=self.optimized_var, value=role.value,
                                       style='Large.TRadiobutton')  # 大字体样式 (20px)
            radio_btn.pack(anchor='w', pady=6)  # 增加间距
        
        # 添加说明
        explanation = ttk.Label(parent, 
                               text="↑ 这是优化后的效果\n文字更大更清晰", 
                               style='Small.TLabel', 
                               justify=tk.CENTER)
        explanation.pack(pady=(20, 0))
    
    def run(self):
        """运行测试窗口"""
        self.root.mainloop()


def main():
    """主函数"""
    print("启动单选按钮字体优化测试...")
    print("=" * 60)
    print("测试内容:")
    print("• 原始字体 vs 优化后字体对比")
    print("• 标签字体: 16px → 20px (+25%)")
    print("• 单选按钮字体: 16px → 20px (+25%)")
    print("• 间距优化: 4px → 6px (+50%)")
    print("=" * 60)
    
    try:
        test_window = RadioButtonFontTestWindow()
        test_window.run()
    except Exception as e:
        print(f"测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
