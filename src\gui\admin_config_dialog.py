"""
管理员配置对话框
提供用户权限管理和系统配置功能
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.user_management.user_permission_system import UserPermissionManager, UserRole, LimitType
from src.gui.theme_manager import theme_manager


class AdminConfigDialog:
    """管理员配置对话框"""
    
    def __init__(self, parent, permission_manager: UserPermissionManager):
        """初始化管理员配置对话框"""
        self.parent = parent
        self.permission_manager = permission_manager
        
        # 检查管理员权限
        if not self._check_admin_permission():
            messagebox.showerror("权限不足", "只有管理员才能访问此功能")
            return
        
        # 创建对话框窗口 - 增大尺寸
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("管理员配置")
        self.dialog.geometry("1000x750")  # 增大尺寸 (800x600 → 1000x750)
        self.dialog.resizable(True, True)
        
        # 应用主题
        theme_manager.apply_theme(self.dialog)
        
        # 设置模态
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self._create_widgets()
        self._center_window()
        self._load_data()
    
    def _check_admin_permission(self) -> bool:
        """检查管理员权限"""
        current_user = self.permission_manager.current_user
        return current_user and current_user.role == UserRole.ADMIN
    
    def _center_window(self):
        """居中显示窗口"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="管理员配置", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # 用户管理选项卡
        self.user_management_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.user_management_frame, text="用户管理")
        self._create_user_management_tab()
        
        # 权限配置选项卡
        self.permission_config_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.permission_config_frame, text="权限配置")
        self._create_permission_config_tab()
        
        # 使用统计选项卡
        self.usage_stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.usage_stats_frame, text="使用统计")
        self._create_usage_stats_tab()
        
        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="保存配置", 
                  command=self._save_config, style='Success.TButton').pack(side=tk.LEFT)
        ttk.Button(button_frame, text="刷新数据", 
                  command=self._load_data, style='TButton').pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(button_frame, text="关闭", 
                  command=self._close_dialog).pack(side=tk.RIGHT)
    
    def _create_user_management_tab(self):
        """创建用户管理选项卡"""
        # 用户列表框架
        list_frame = ttk.LabelFrame(self.user_management_frame, text="用户列表")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建用户列表
        columns = ("用户名", "角色", "注册时间", "最后登录", "邮箱数", "验证码数", "状态")
        self.user_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)
        
        # 设置列标题和宽度
        for col in columns:
            self.user_tree.heading(col, text=col)
            self.user_tree.column(col, width=100)
        
        # 添加滚动条
        user_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.user_tree.yview)
        self.user_tree.configure(yscrollcommand=user_scrollbar.set)
        
        self.user_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        user_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)
        
        # 用户操作按钮
        user_button_frame = ttk.Frame(self.user_management_frame)
        user_button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(user_button_frame, text="创建用户", 
                  command=self._create_user).pack(side=tk.LEFT)
        ttk.Button(user_button_frame, text="编辑用户", 
                  command=self._edit_user).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(user_button_frame, text="禁用/启用", 
                  command=self._toggle_user_status).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(user_button_frame, text="重置统计", 
                  command=self._reset_user_stats).pack(side=tk.LEFT, padx=(10, 0))
    
    def _create_permission_config_tab(self):
        """创建权限配置选项卡"""
        # 角色选择
        role_frame = ttk.Frame(self.permission_config_frame)
        role_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(role_frame, text="选择角色:", style='TLabel').pack(side=tk.LEFT)
        self.role_var = tk.StringVar(value=UserRole.BASIC.value)
        role_combo = ttk.Combobox(role_frame, textvariable=self.role_var, 
                                 values=[role.value for role in UserRole], state="readonly")
        role_combo.pack(side=tk.LEFT, padx=(10, 0))
        role_combo.bind('<<ComboboxSelected>>', self._on_role_selected)
        
        # 权限配置框架
        config_frame = ttk.LabelFrame(self.permission_config_frame, text="权限配置")
        config_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 邮箱限制配置
        email_frame = ttk.Frame(config_frame)
        email_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(email_frame, text="邮箱限制:", width=15).pack(side=tk.LEFT)
        self.email_limit_var = tk.StringVar()
        ttk.Entry(email_frame, textvariable=self.email_limit_var, width=10).pack(side=tk.LEFT, padx=(10, 0))
        
        # 验证码限制配置
        verification_frame = ttk.Frame(config_frame)
        verification_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(verification_frame, text="验证码限制:", width=15).pack(side=tk.LEFT)
        self.verification_limit_var = tk.StringVar()
        ttk.Entry(verification_frame, textvariable=self.verification_limit_var, width=10).pack(side=tk.LEFT, padx=(10, 0))
        
        # 限制类型配置
        limit_type_frame = ttk.Frame(config_frame)
        limit_type_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(limit_type_frame, text="限制类型:", width=15).pack(side=tk.LEFT)
        self.limit_type_var = tk.StringVar()
        limit_type_combo = ttk.Combobox(limit_type_frame, textvariable=self.limit_type_var,
                                       values=[lt.value for lt in LimitType], state="readonly")
        limit_type_combo.pack(side=tk.LEFT, padx=(10, 0))
        
        # 频率限制配置
        rate_frame = ttk.Frame(config_frame)
        rate_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(rate_frame, text="操作间隔(秒):", width=15).pack(side=tk.LEFT)
        self.rate_limit_var = tk.StringVar()
        ttk.Entry(rate_frame, textvariable=self.rate_limit_var, width=10).pack(side=tk.LEFT, padx=(10, 0))
        
        # 最大会话数配置
        session_frame = ttk.Frame(config_frame)
        session_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(session_frame, text="最大会话数:", width=15).pack(side=tk.LEFT)
        self.max_sessions_var = tk.StringVar()
        ttk.Entry(session_frame, textvariable=self.max_sessions_var, width=10).pack(side=tk.LEFT, padx=(10, 0))
    
    def _create_usage_stats_tab(self):
        """创建使用统计选项卡"""
        # 统计信息显示
        stats_frame = ttk.LabelFrame(self.usage_stats_frame, text="系统统计")
        stats_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.stats_text = tk.Text(stats_frame, height=20, width=80)
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)
        
        # 统计操作按钮
        stats_button_frame = ttk.Frame(self.usage_stats_frame)
        stats_button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(stats_button_frame, text="刷新统计", 
                  command=self._refresh_stats).pack(side=tk.LEFT)
        ttk.Button(stats_button_frame, text="导出统计", 
                  command=self._export_stats).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(stats_button_frame, text="清理日志", 
                  command=self._clean_logs).pack(side=tk.LEFT, padx=(10, 0))
    
    def _load_data(self):
        """加载数据"""
        self._load_user_list()
        self._load_permission_config()
        self._refresh_stats()
    
    def _load_user_list(self):
        """加载用户列表"""
        # 清空现有数据
        for item in self.user_tree.get_children():
            self.user_tree.delete(item)
        
        # 添加用户数据
        for user_info in self.permission_manager.users.values():
            role_desc = {
                UserRole.GUEST: "访客",
                UserRole.BASIC: "基础",
                UserRole.PREMIUM: "高级",
                UserRole.VIP: "VIP",
                UserRole.ADMIN: "管理员"
            }.get(user_info.role, "未知")
            
            status = "正常" if user_info.is_active else "禁用"
            
            self.user_tree.insert("", "end", values=(
                user_info.username,
                role_desc,
                user_info.created_at.strftime("%Y-%m-%d"),
                user_info.last_login.strftime("%Y-%m-%d %H:%M"),
                user_info.email_count,
                user_info.verification_count,
                status
            ))
    
    def _load_permission_config(self):
        """加载权限配置"""
        self._on_role_selected()
    
    def _on_role_selected(self, event=None):
        """角色选择事件"""
        role = UserRole(self.role_var.get())
        permission = self.permission_manager.permissions.get(role)
        
        if permission:
            self.email_limit_var.set(str(permission.email_limit))
            self.verification_limit_var.set(str(permission.verification_limit))
            self.limit_type_var.set(permission.limit_type.value)
            self.rate_limit_var.set(str(permission.rate_limit_seconds))
            self.max_sessions_var.set(str(permission.max_concurrent_sessions))
    
    def _refresh_stats(self):
        """刷新统计信息"""
        stats_text = "系统使用统计\n"
        stats_text += "=" * 50 + "\n\n"
        
        # 用户统计
        total_users = len(self.permission_manager.users)
        active_users = sum(1 for u in self.permission_manager.users.values() if u.is_active)
        
        stats_text += f"用户统计:\n"
        stats_text += f"  总用户数: {total_users}\n"
        stats_text += f"  活跃用户: {active_users}\n"
        stats_text += f"  禁用用户: {total_users - active_users}\n\n"
        
        # 角色分布
        role_counts = {}
        for user in self.permission_manager.users.values():
            role_counts[user.role] = role_counts.get(user.role, 0) + 1
        
        stats_text += "角色分布:\n"
        for role, count in role_counts.items():
            role_name = {
                UserRole.GUEST: "访客",
                UserRole.BASIC: "基础用户",
                UserRole.PREMIUM: "高级用户",
                UserRole.VIP: "VIP用户",
                UserRole.ADMIN: "管理员"
            }.get(role, "未知")
            stats_text += f"  {role_name}: {count}\n"
        
        stats_text += f"\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)
    
    def _create_user(self):
        """创建用户"""
        messagebox.showinfo("功能开发中", "用户创建功能正在开发中")
    
    def _edit_user(self):
        """编辑用户"""
        messagebox.showinfo("功能开发中", "用户编辑功能正在开发中")
    
    def _toggle_user_status(self):
        """切换用户状态"""
        messagebox.showinfo("功能开发中", "用户状态切换功能正在开发中")
    
    def _reset_user_stats(self):
        """重置用户统计"""
        messagebox.showinfo("功能开发中", "用户统计重置功能正在开发中")
    
    def _export_stats(self):
        """导出统计"""
        messagebox.showinfo("功能开发中", "统计导出功能正在开发中")
    
    def _clean_logs(self):
        """清理日志"""
        messagebox.showinfo("功能开发中", "日志清理功能正在开发中")
    
    def _save_config(self):
        """保存配置"""
        try:
            role = UserRole(self.role_var.get())
            permission = self.permission_manager.permissions.get(role)
            
            if permission:
                permission.email_limit = int(self.email_limit_var.get())
                permission.verification_limit = int(self.verification_limit_var.get())
                permission.limit_type = LimitType(self.limit_type_var.get())
                permission.rate_limit_seconds = int(self.rate_limit_var.get())
                permission.max_concurrent_sessions = int(self.max_sessions_var.get())
                
                self.permission_manager._save_permissions()
                messagebox.showinfo("成功", "配置已保存")
            
        except ValueError as e:
            messagebox.showerror("错误", f"配置保存失败: {e}")
    
    def _close_dialog(self):
        """关闭对话框"""
        self.dialog.grab_release()
        self.dialog.destroy()
    
    def show(self):
        """显示对话框"""
        self.dialog.wait_window()


def main():
    """测试管理员配置对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 创建权限管理器并设置管理员用户
    permission_manager = UserPermissionManager()
    admin_id = permission_manager.create_user("admin", UserRole.ADMIN, "系统管理员")
    permission_manager.login_user("admin")
    
    admin_dialog = AdminConfigDialog(root, permission_manager)
    admin_dialog.show()
    
    root.destroy()


if __name__ == "__main__":
    main()
