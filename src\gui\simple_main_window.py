"""
简化版主窗口 - 只包含邮箱生成和验证码获取功能
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Optional
import os
from src.gui.simple_email_panel import SimpleEmailPanel
from src.gui.simple_verification_panel import SimpleVerificationPanel
from src.gui.status_bar import StatusBar
from src.gui.theme_manager import theme_manager
# from src.user_management.user_permission_system import UserPermissionManager  # 暂时注释掉用户认证功能
# from src.user_management.permission_decorators import get_permission_checker  # 暂时注释掉用户认证功能
# from src.gui.user_auth_dialog import UserAuthDialog  # 暂时注释掉用户认证功能


class SimpleMainWindow:
    """简化版主窗口类 - 只包含核心功能"""
    
    def __init__(self, app):
        self.app = app  # 应用程序实例
        self.root = tk.Tk()

        # # 初始化权限管理 - 暂时注释掉用户认证功能
        # self.permission_manager = UserPermissionManager()
        # self.permission_checker = get_permission_checker(self.permission_manager)

        self._setup_window()
        self._create_menu()
        self._create_widgets()
        self._setup_logging()

        # # 显示用户认证对话框 - 暂时注释掉用户认证功能
        # self._show_initial_auth()
    
    def _setup_window(self):
        """设置窗口属性"""
        self.root.title("AugmentCode自动注册助手 - 简化版")
        self.root.geometry("1200x900")  # 增大窗口大小 (800x600 → 1200x900)
        self.root.minsize(1000, 750)    # 增大最小窗口大小 (600x450 → 1000x750)
        
        # 应用主题
        self.style = theme_manager.apply_theme(self.root)

    def _create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # # 用户菜单 - 暂时隐藏用户认证相关功能
        # user_menu = tk.Menu(menubar, tearoff=0)
        # menubar.add_cascade(label="用户", menu=user_menu)
        # user_menu.add_command(label="用户登录", command=self._show_auth_dialog)
        # user_menu.add_command(label="用户统计", command=self._show_user_stats)
        # user_menu.add_separator()
        # user_menu.add_command(label="退出登录", command=self._logout_user)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        # help_menu.add_command(label="权限说明", command=self._show_permission_help)  # 暂时隐藏权限说明
        help_menu.add_command(label="关于", command=self._show_about)
        
        # 设置窗口图标（如果有的话）
        try:
            icon_path = os.path.join(os.path.dirname(__file__), '..', '..', 'icon.ico')
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception:
            pass
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # 绑定窗口大小变化事件
        self.root.bind('<Configure>', self._on_window_resize)
        
        # 居中显示窗口
        self._center_window()
        
        # 初始化响应式布局变量
        self._last_width = 800
        self._last_height = 600
        self._is_compact_mode = False
    
    def _center_window(self):
        """居中显示窗口"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def _on_window_resize(self, event):
        """窗口大小变化事件处理"""
        # 只处理主窗口的resize事件
        if event.widget != self.root:
            return
            
        current_width = self.root.winfo_width()
        current_height = self.root.winfo_height()
        
        # 检查是否需要切换到紧凑模式
        compact_threshold = 700  # 简化版的紧凑模式阈值
        should_be_compact = current_width < compact_threshold
        
        if should_be_compact != self._is_compact_mode:
            self._is_compact_mode = should_be_compact
            self._apply_responsive_layout()
        
        self._last_width = current_width
        self._last_height = current_height
    
    def _apply_responsive_layout(self):
        """应用响应式布局"""
        if self._is_compact_mode:
            # 紧凑模式：减少内边距，调整组件大小
            self._apply_compact_layout()
        else:
            # 正常模式：恢复默认布局
            self._apply_normal_layout()
    
    def _apply_compact_layout(self):
        """应用紧凑布局"""
        # 减少主框架的内边距
        for child in self.root.winfo_children():
            if isinstance(child, ttk.Frame):
                child.pack_configure(padx=5, pady=5)
        
        # 通知各个面板切换到紧凑模式
        if hasattr(self, 'email_panel'):
            self.email_panel.set_compact_mode(True)
        if hasattr(self, 'verification_panel'):
            self.verification_panel.set_compact_mode(True)
    
    def _apply_normal_layout(self):
        """应用正常布局"""
        # 恢复主框架的内边距
        for child in self.root.winfo_children():
            if isinstance(child, ttk.Frame):
                child.pack_configure(padx=15, pady=15)
        
        # 通知各个面板切换到正常模式
        if hasattr(self, 'email_panel'):
            self.email_panel.set_compact_mode(False)
        if hasattr(self, 'verification_panel'):
            self.verification_panel.set_compact_mode(False)
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建主框架 - 第三轮增大边距适应更大字体和窗口
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=35, pady=35)  # 25 → 35
        
        # 创建标题区域 - 第三轮增大底部间距
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 40))  # 30 → 40
        
        title_label = ttk.Label(title_frame, text="自动注册助手",
                               style='Title.TLabel')
        title_label.pack()

        subtitle_label = ttk.Label(title_frame, text="邮箱生成 & 验证码获取",
                                  style='Subtitle.TLabel')
        subtitle_label.pack(pady=(12, 0))  # 8 → 12
        
        # 创建笔记本控件（标签页）- 只包含两个核心功能，第三轮增大间距
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 30))  # 20 → 30
        
        # 创建简化版邮箱生成面板
        self.email_panel = SimpleEmailPanel(self.notebook, self)
        
        # 创建简化版验证码获取面板
        self.verification_panel = SimpleVerificationPanel(self.notebook, self)
        
        # 添加标签页
        self.notebook.add(self.email_panel.frame, text="📧 邮箱生成")
        self.notebook.add(self.verification_panel.frame, text="🔐 验证码获取")
        
        # 创建状态栏
        self.status_bar = StatusBar(main_frame, self)
        self.status_bar.frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))
        
        # 添加配置状态显示
        self._create_config_status()
    
    def _create_config_status(self):
        """创建配置状态显示"""
        config_frame = ttk.Frame(self.status_bar.frame)
        config_frame.pack(side=tk.RIGHT, padx=(10, 0))
        
        # # 显示当前配置信息
        # config_info = f"用户: {self.app.app_config.temp_mail.username} | "
        # config_info += f"域名: {self.app.app_config.email_domain}"
        
        # config_label = ttk.Label(config_frame, text=config_info,
                                # style='Small.TLabel')
        # config_label.pack()
    
    def _setup_logging(self):
        """设置日志"""
        # 添加日志回调，更新状态栏
        self.app.log_manager.add_callback(self._on_log_added)
    
    def _on_log_added(self, log_entry):
        """日志添加回调"""
        # 更新状态栏
        if hasattr(self, 'status_bar') and log_entry:
            self.status_bar.set_status(log_entry.message, log_entry.level)
    
    def save_config(self, config):
        """保存配置（简化版中不需要，但保留接口兼容性）"""
        return self.app.save_config(config)
    
    def generate_email(self, count: int = 1):
        """生成邮箱"""
        # # 检查用户登录状态 - 暂时注释掉权限检查功能
        # if not self.permission_manager.current_user:
        #     messagebox.showerror("权限错误", "请先登录用户账号")
        #     return None

        # # 检查邮箱注册限制 - 暂时注释掉权限检查功能
        # allowed, reason, used, limit = self.permission_manager.check_email_limit()
        # if not allowed:
        #     if limit == -1:
        #         limit_text = "无限制"
        #     else:
        #         limit_text = f"{limit}个"
        #     messagebox.showwarning("限制提醒",
        #                          f"{reason}\n当前已使用: {used}\n限制数量: {limit_text}")
        #     return None

        # # 检查操作频率限制 - 暂时注释掉权限检查功能
        # rate_allowed, rate_reason, _ = self.permission_manager.check_rate_limit()
        # if not rate_allowed:
        #     messagebox.showwarning("频率限制", rate_reason)
        #     return None

        # 执行邮箱生成
        try:
            # 直接调用邮箱生成逻辑，不使用线程
            if not self.app.email_generator:
                messagebox.showerror("错误", "邮箱生成器未初始化，请先配置应用")
                return None

            if count < 1 or count > 100:
                messagebox.showerror("错误", "生成数量必须在1-100之间")
                return None

            print(f"开始生成{count}个邮箱...")
            if count == 1:
                email_info = self.app.email_generator.generate_single_email()
                emails = [email_info]
                print(f"生成的邮箱: {email_info.address}")
            else:
                emails = self.app.email_generator.generate_batch_emails(count)
                print(f"生成的邮箱列表: {[e.address for e in emails]}")

            # 更新邮箱面板
            print(f"检查email_panel属性: {hasattr(self, 'email_panel')}")
            if hasattr(self, 'email_panel'):
                print("调用email_panel.add_emails方法...")
                self.email_panel.add_emails(emails)
                print("email_panel.add_emails方法调用完成")

            self.app.log_manager.add_log(f"成功生成{len(emails)}个邮箱", "SUCCESS")

            # # 记录使用情况 - 暂时注释掉权限管理功能
            # self.permission_manager.record_email_generation()

            return emails
        except Exception as e:
            print(f"邮箱生成异常: {e}")
            messagebox.showerror("错误", f"邮箱生成失败: {str(e)}")
            return None



    def get_verification_code(self):
        """获取验证码"""
        self.app.get_verification_code()
    
    def _on_closing(self):
        """窗口关闭事件"""
        try:
            self.app.log_manager.add_log("应用程序关闭", "INFO")
            self.root.quit()
            self.root.destroy()
        except:
            pass
    
    def run(self):
        """运行应用"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self._on_closing()
        except Exception as e:
            messagebox.showerror("严重错误", f"应用程序发生严重错误: {str(e)}")
            self._on_closing()

    # def _show_initial_auth(self):
    #     """显示初始用户认证对话框 - 暂时注释掉用户认证功能"""
    #     # 延迟显示认证对话框，确保主窗口已完全加载
    #     self.root.after(500, self._show_auth_dialog)

    # def _show_auth_dialog(self):
    #     """显示用户认证对话框 - 暂时注释掉用户认证功能"""
    #     auth_dialog = UserAuthDialog(self.root)
    #     result, user_info = auth_dialog.show()

    #     if result and user_info:
    #         # 重要：设置当前用户到permission_manager中，确保引用同一个对象
    #         self.permission_manager.current_user = self.permission_manager.users.get(user_info.user_id, user_info)

    #         # 更新窗口标题显示当前用户
    #         role_desc = {
    #             'guest': '访客',
    #             'trial': '体验用户',
    #             'basic': '基础用户',
    #             'premium': '高级用户',
    #             'vip': 'VIP用户',
    #             'admin': '管理员'
    #         }.get(user_info.role.value, '未知')

    #         self.root.title(f"AugmentCode自动注册助手 - {user_info.username} ({role_desc})")

    #         # 更新状态栏
    #         if hasattr(self, 'status_bar'):
    #             self.status_bar.set_status(f"已登录: {user_info.username} ({role_desc})")

    #         # 加载用户的历史邮箱
    #         if hasattr(self, 'email_panel'):
    #             self.email_panel.load_user_emails(user_info.user_id)
    #     else:
    #         # 用户取消认证，关闭应用
    #         messagebox.showinfo("提示", "需要用户认证才能使用本应用")
    #         self.root.quit()

    # def _show_user_stats(self):
    #     """显示用户统计信息 - 暂时注释掉用户认证功能"""
    #     self.permission_checker.show_user_stats()

    # def _logout_user(self):
    #     """用户登出 - 暂时注释掉用户认证功能"""
    #     if self.permission_manager.current_user:
    #         username = self.permission_manager.current_user.username
    #         self.permission_manager.logout_user()

    #         # 清空邮箱列表（不删除存储文件）
    #         if hasattr(self, 'email_panel'):
    #             self.email_panel.emails.clear()
    #             self.email_panel._refresh_tree()

    #         # 恢复窗口标题
    #         self.root.title("AugmentCode自动注册助手 - 简化版")

    #         # 更新状态栏
    #         if hasattr(self, 'status_bar'):
    #             self.status_bar.set_status("已退出登录")

    #         messagebox.showinfo("退出登录", f"用户 {username} 已退出登录")

    #         # 重新显示认证对话框
    #         self.root.after(1000, self._show_auth_dialog)
    #     else:
    #         messagebox.showinfo("提示", "当前没有用户登录")

    # def _show_permission_help(self):
    #     """显示权限说明 - 暂时注释掉用户认证功能"""
    #     help_text = """用户权限说明:

    # 访客用户:
    # • 每日1个邮箱，3次验证码
    # • 操作间隔60秒
    # • 基础功能

    # 基础用户:
    # • 每日5个邮箱，10次验证码
    # • 操作间隔30秒
    # • 基础功能 + 验证码获取

    # 高级用户:
    # • 每日20个邮箱，50次验证码
    # • 操作间隔10秒
    # • 基础功能 + 验证码获取 + 批量生成

    # VIP用户:
    # • 每日100个邮箱，200次验证码
    # • 操作间隔5秒
    # • 所有功能 + 自定义域名

    # 管理员:
    # • 无限制使用
    # • 所有功能

    # 注意: 限制按自然日计算，每日0点重置。"""

    #     messagebox.showinfo("权限说明", help_text)

    def _show_about(self):
        """显示关于信息"""
        about_text = """AugmentCode自动注册助手 v2.0 - 简化版

功能特点:
• 智能邮箱生成
• 自动验证码获取
• 简洁易用界面

开发: AugmentCode Team
版权所有 © 2024"""

        messagebox.showinfo("关于", about_text)
