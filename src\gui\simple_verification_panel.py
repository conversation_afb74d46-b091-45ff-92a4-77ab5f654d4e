"""
简化版验证码获取面板
"""
import tkinter as tk
from tkinter import ttk, messagebox
import pyperclip
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.gui.theme_manager import theme_manager
from src.gui.custom_widgets import ModernButton, VerificationCodeDisplay
from src.gui.layout_manager import LayoutManager
from src.user_management.user_permission_system import UserPermissionManager
from src.user_management.permission_decorators import get_permission_checker
from src.user_management.user_permission_system import UserRole
from src.gui.user_auth_dialog import UserAuthDialog


class SimpleVerificationPanel:
    """简化版验证码获取面板"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.current_code = None
        self._is_compact_mode = False

        # 初始化权限管理
        self.permission_manager = UserPermissionManager()
        self.permission_checker = get_permission_checker(self.permission_manager)

        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面组件"""
        self.frame = ttk.Frame(self.parent)
        self.frame.configure(style='TFrame')
        
        # 创建说明文本区域
        self.info_frame = ttk.Frame(self.frame)
        self.info_frame.pack(fill=tk.X, padx=20, pady=(20, 15))
        
        info_text = ("此功能从tempmail.plus获取最新邮件中的验证码。\n"
                    "当前配置已自动设置，无需手动配置。")
        self.info_label = ttk.Label(self.info_frame, text=info_text,
                                   justify=tk.CENTER,
                                   wraplength=500,
                                   style='TLabel')
        self.info_label.pack(anchor='center')
        
        # 创建控制按钮区域
        self.control_frame = ttk.Frame(self.frame)
        self.control_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        # 获取验证码按钮
        self.get_code_btn = ModernButton(self.control_frame, text="获取验证码", 
                                        command=self._on_get_code_click,
                                        style='Primary.TButton')
        self.get_code_btn.pack(anchor='center')
        
        # 创建验证码显示区域
        self.code_display_frame = ttk.LabelFrame(self.frame, text="当前验证码")
        self.code_display_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        # 验证码显示框
        self.code_display = VerificationCodeDisplay(self.code_display_frame)
        self.code_display.pack(fill=tk.X, padx=15, pady=15)
        
        # 创建操作按钮区域
        self.action_frame = ttk.Frame(self.frame)
        self.action_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        # 复制按钮
        self.copy_btn = ModernButton(self.action_frame, text="复制验证码", 
                                    command=self._copy_code,
                                    style='Success.TButton',
                                    state='disabled')
        self.copy_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清除按钮
        self.clear_btn = ModernButton(self.action_frame, text="清除验证码", 
                                     command=self._clear_code,
                                     style='Secondary.TButton',
                                     state='disabled')
        self.clear_btn.pack(side=tk.LEFT)
        
        # 创建状态信息区域
        self.status_frame = ttk.Frame(self.frame)
        self.status_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        self.status_label = ttk.Label(self.status_frame, text="准备就绪，点击获取验证码开始",
                                     style='Small.TLabel')
        self.status_label.pack(anchor='center')
        
        # # 创建配置信息显示
        # self.config_info_frame = ttk.LabelFrame(self.frame, text="当前配置")
        # self.config_info_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        #
        # # 显示当前配置信息
        # config = self.main_window.app.app_config
        # config_text = f"用户名: {config.temp_mail.username}\n"
        # config_text += f"邮箱扩展: {config.temp_mail.email_extension}\n"
        # config_text += f"邮箱域名: {config.email_domain}"
        #
        # self.config_label = ttk.Label(self.config_info_frame, text=config_text,
        #                              justify=tk.LEFT,
        #                              style='Small.TLabel')
        # self.config_label.pack(padx=15, pady=10, anchor='w')
    
    def _on_get_code_click(self):
        """获取验证码按钮点击事件"""
        # 检查用户是否已登录，如果没有则自动以访客身份登录
        if not self.permission_manager.current_user:
            success = self._auto_guest_login()
            if not success:
                self._show_auth_dialog()
                return

        # 检查验证码获取权限
        allowed, reason, used, limit = self.permission_manager.check_verification_limit()
        if not allowed:
            if limit == -1:
                limit_text = "无限制"
            else:
                limit_text = f"{limit}次"
            messagebox.showwarning("限制提醒",
                                 f"{reason}\n当前已使用: {used}\n限制次数: {limit_text}")
            return

        # 检查操作频率限制
        rate_allowed, rate_reason, wait_time = self.permission_manager.check_rate_limit()
        if not rate_allowed:
            messagebox.showwarning("频率限制", rate_reason)
            return

        # 禁用按钮防止重复点击
        self.get_code_btn.configure(state='disabled', text="获取中...")
        self.status_label.configure(text="正在获取验证码，请稍候...")

        # 启动获取验证码
        success = self.main_window.get_verification_code()

        # 如果操作成功，记录使用
        if success:
            self.permission_manager.record_verification_request()

        # 设置定时器恢复按钮状态
        self.main_window.root.after(3000, self._restore_button_state)
    
    def _restore_button_state(self):
        """恢复按钮状态"""
        self.get_code_btn.configure(state='normal', text="获取验证码")
        if not self.current_code:
            self.status_label.configure(text="获取完成，请检查验证码显示区域")
    
    def _copy_code(self):
        """复制验证码"""
        if self.current_code:
            try:
                pyperclip.copy(self.current_code)
                self.main_window.app.log_manager.add_log(f"已复制验证码: {self.current_code}", "SUCCESS")
                self.status_label.configure(text="验证码已复制到剪贴板")
                
                # 显示复制成功的视觉反馈
                original_text = self.copy_btn.cget('text')
                self.copy_btn.configure(text="已复制!")
                
                # 2秒后恢复原始文本
                self.main_window.root.after(2000, lambda: self.copy_btn.configure(text=original_text))
                
            except Exception as e:
                messagebox.showerror("错误", f"复制失败: {str(e)}")
        else:
            messagebox.showwarning("提示", "没有验证码可复制")

    def _auto_guest_login(self):
        """自动以访客身份登录"""
        try:
            # 创建或获取访客用户
            guest_username = "guest_user"

            # 尝试登录现有访客用户
            user_info = self.permission_manager.login_user(guest_username)

            if not user_info:
                # 创建新的访客用户
                try:
                    user_id = self.permission_manager.create_user(guest_username, UserRole.GUEST, "访客用户")
                    user_info = self.permission_manager.login_user(guest_username)
                except ValueError:
                    # 访客用户已存在但未激活，直接获取
                    for user in self.permission_manager.users.values():
                        if user.username == guest_username:
                            user.is_active = True
                            user_info = self.permission_manager.login_user(guest_username)
                            break

            if user_info:
                self.status_label.configure(text="已自动登录为访客用户")
                return True
            else:
                return False

        except Exception as e:
            print(f"自动访客登录失败: {e}")
            return False

    def _show_auth_dialog(self):
        """显示用户认证对话框"""
        auth_dialog = UserAuthDialog(self.main_window.root)
        result, user_info = auth_dialog.show()

        if result and user_info:
            # 更新状态标签显示当前用户信息
            role_desc = {
                'guest': '访客',
                'trial': '体验用户',
                'basic': '基础用户',
                'premium': '高级用户',
                'vip': 'VIP用户',
                'admin': '管理员'
            }.get(user_info.role.value, '未知')

            self.status_label.configure(text=f"已登录: {user_info.username} ({role_desc})")

            # 显示用户权限信息
            self._show_user_permission_info()
        else:
            self.status_label.configure(text="需要登录才能使用验证码功能")

    def _show_user_permission_info(self):
        """显示用户权限信息"""
        if not self.permission_manager.current_user:
            return

        stats = self.permission_manager.get_user_stats()
        if not stats:
            return

        limits = stats['limits']

        # 构建权限信息
        info_parts = []

        # 验证码限制信息
        verification_limit = limits['verification']
        if verification_limit['limit'] == -1:
            info_parts.append("验证码: 无限制")
        else:
            remaining = verification_limit['limit'] - verification_limit['used']
            info_parts.append(f"验证码: 剩余{remaining}次")

        # 频率限制信息
        rate_limit = limits['rate_limit']
        if rate_limit['limit_seconds'] > 0:
            info_parts.append(f"间隔: {rate_limit['limit_seconds']}秒")

        # 更新配置信息显示
        if hasattr(self, 'config_label'):
            permission_info = " | ".join(info_parts)
            config_text = f"当前权限: {permission_info}\n点击右上角可查看详细统计信息"
            self.config_label.configure(text=config_text)
    
    def _clear_code(self):
        """清除验证码"""
        self.current_code = None
        self.code_display.set_code("")
        self.copy_btn.configure(state='disabled')
        self.clear_btn.configure(state='disabled')
        self.status_label.configure(text="验证码已清除")
        self.main_window.app.log_manager.add_log("验证码已清除", "INFO")
    
    def set_verification_code(self, code):
        """设置验证码"""
        if code and code.strip():
            self.current_code = code.strip()
            self.code_display.set_code(self.current_code)
            self.copy_btn.configure(state='normal')
            self.clear_btn.configure(state='normal')
            self.status_label.configure(text=f"验证码获取成功: {self.current_code}")
        else:
            self.current_code = None
            self.code_display.set_code("")
            self.copy_btn.configure(state='disabled')
            self.clear_btn.configure(state='disabled')
            self.status_label.configure(text="未获取到验证码，请重试")
    
    def set_compact_mode(self, is_compact):
        """设置紧凑模式"""
        if self._is_compact_mode == is_compact:
            return
            
        self._is_compact_mode = is_compact
        
        if is_compact:
            self._apply_compact_layout()
        else:
            self._apply_normal_layout()
    
    def _apply_compact_layout(self):
        """应用紧凑布局"""
        # 减少内边距
        for child in self.frame.winfo_children():
            if isinstance(child, ttk.Frame) or isinstance(child, ttk.LabelFrame):
                child.pack_configure(padx=10, pady=5)
        
        # 调整按钮文本
        self.get_code_btn.configure(text="获取")
        self.copy_btn.configure(text="复制")
        self.clear_btn.configure(text="清除")
    
    def _apply_normal_layout(self):
        """应用正常布局"""
        # 恢复内边距
        for child in self.frame.winfo_children():
            if isinstance(child, ttk.Frame) or isinstance(child, ttk.LabelFrame):
                child.pack_configure(padx=20, pady=10)
        
        # 恢复按钮文本
        self.get_code_btn.configure(text="获取验证码")
        self.copy_btn.configure(text="复制验证码")
        self.clear_btn.configure(text="清除验证码")
