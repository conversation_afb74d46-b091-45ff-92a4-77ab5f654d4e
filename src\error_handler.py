"""
错误处理工具
"""
import sys
import traceback
import threading
from tkinter import messagebox
from typing import Optional, Callable, Dict, Type, Any

from src.exceptions import (
    BaseAppError, TempMailAPIError, ConfigurationError, 
    NetworkError, VerificationCodeError, ValidationError,
    FileOperationError, WorkflowError, UIError
)


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, log_manager=None):
        """初始化错误处理器"""
        self.log_manager = log_manager
        self.error_handlers: Dict[Type[Exception], Callable] = {}
        self.default_handler: Optional[Callable] = None
        
        # 注册默认的错误处理器
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """注册默认的错误处理器"""
        # 网络错误处理器
        self.register_handler(NetworkError, self._handle_network_error)
        
        # 配置错误处理器
        self.register_handler(ConfigurationError, self._handle_config_error)
        
        # API错误处理器
        self.register_handler(TempMailAPIError, self._handle_api_error)
        
        # 验证码错误处理器
        self.register_handler(VerificationCodeError, self._handle_verification_error)
        
        # 验证错误处理器
        self.register_handler(ValidationError, self._handle_validation_error)
        
        # 文件操作错误处理器
        self.register_handler(FileOperationError, self._handle_file_error)
        
        # 工作流程错误处理器
        self.register_handler(WorkflowError, self._handle_workflow_error)
        
        # UI错误处理器
        self.register_handler(UIError, self._handle_ui_error)
        
        # 设置默认处理器
        self.set_default_handler(self._handle_default_error)
    
    def register_handler(self, exception_type: Type[Exception], handler: Callable):
        """注册错误处理器"""
        self.error_handlers[exception_type] = handler
    
    def set_default_handler(self, handler: Callable):
        """设置默认错误处理器"""
        self.default_handler = handler
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """处理异常"""
        # 记录错误日志
        self._log_exception(exc_type, exc_value, exc_traceback)
        
        # 查找对应的处理器
        handler = self._find_handler(exc_type)
        
        # 调用处理器
        try:
            handler(exc_value, exc_traceback)
        except Exception as e:
            # 处理器本身出错，使用最基本的错误处理
            print(f"错误处理器失败: {e}")
            self._basic_error_handling(exc_value)
        
        # 在非主线程中，调用原始的异常处理器
        if not isinstance(threading.current_thread(), threading._MainThread):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
    
    def _find_handler(self, exc_type) -> Callable:
        """查找对应的错误处理器"""
        # 直接匹配
        if exc_type in self.error_handlers:
            return self.error_handlers[exc_type]
        
        # 基于继承关系匹配
        for exception_cls, handler in self.error_handlers.items():
            if issubclass(exc_type, exception_cls):
                return handler
        
        # 使用默认处理器
        return self.default_handler or self._basic_error_handling
    
    def _log_exception(self, exc_type, exc_value, exc_traceback):
        """记录异常日志"""
        error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        if self.log_manager:
            self.log_manager.add_log(f"异常: {exc_value}", "ERROR")
            
            # 记录详细的堆栈跟踪
            for line in error_msg.split('\n'):
                if line.strip():
                    self.log_manager.add_log(f"堆栈: {line}", "ERROR")
        else:
            # 如果没有日志管理器，至少打印到控制台
            print(f"错误: {exc_value}")
            print(error_msg)
    
    def _basic_error_handling(self, exc_value):
        """最基本的错误处理"""
        try:
            messagebox.showerror("错误", f"应用程序发生错误:\n{exc_value}")
        except:
            print(f"严重错误: {exc_value}")
    
    def _handle_network_error(self, exc_value, exc_traceback):
        """处理网络错误"""
        message = f"网络连接错误: {exc_value}\n\n请检查您的网络连接并重试。"
        self._show_error_dialog("网络错误", message)
    
    def _handle_config_error(self, exc_value, exc_traceback):
        """处理配置错误"""
        error_str = str(exc_value)
        
        # 根据错误消息提供具体的解决建议
        suggestion = "请检查您的配置设置并重试。"
        
        if "RawConfigParser.get()" in error_str:
            suggestion = "配置文件格式有误。应用程序将使用默认配置，请重新配置应用。"
        elif "缺少" in error_str and "节" in error_str:
            suggestion = "配置文件缺少必要的配置节。请打开配置面板重新设置您的配置。"
        elif "用户名不能为空" in error_str:
            suggestion = "请在配置面板中设置临时邮箱用户名。"
        elif "邮箱扩展名" in error_str:
            suggestion = "请确保邮箱扩展名格式正确，应以@开头，例如：@fexpost.com"
        elif "epin" in error_str:
            suggestion = "请在配置面板中设置正确的epin值。"
        elif "重试次数" in error_str:
            suggestion = "重试次数必须是1-10之间的整数。"
        elif "重试间隔" in error_str:
            suggestion = "重试间隔必须是1000-10000毫秒之间的整数。"
        elif "无法创建配置目录" in error_str:
            suggestion = "无法创建配置目录，请确保应用程序有足够的权限或手动创建配置目录。"
        
        message = f"配置错误: {exc_value}\n\n{suggestion}"
        self._show_error_dialog("配置错误", message)
    
    def _handle_api_error(self, exc_value, exc_traceback):
        """处理API错误"""
        message = f"临时邮箱API错误: {exc_value}\n\n请检查您的API配置并重试。"
        self._show_error_dialog("API错误", message)
    
    def _handle_verification_error(self, exc_value, exc_traceback):
        """处理验证码错误"""
        message = f"验证码获取错误: {exc_value}\n\n请确保邮箱中有包含验证码的邮件。"
        self._show_error_dialog("验证码错误", message)
    
    def _handle_validation_error(self, exc_value, exc_traceback):
        """处理验证错误"""
        message = f"数据验证错误: {exc_value}\n\n请检查您输入的数据是否正确。"
        self._show_error_dialog("验证错误", message)
    
    def _handle_file_error(self, exc_value, exc_traceback):
        """处理文件操作错误"""
        message = f"文件操作错误: {exc_value}\n\n请确保您有足够的权限访问所需文件。"
        self._show_error_dialog("文件错误", message)
    
    def _handle_workflow_error(self, exc_value, exc_traceback):
        """处理工作流程错误"""
        message = f"工作流程错误: {exc_value}\n\n操作未能完成，请重试。"
        self._show_error_dialog("工作流程错误", message)
    
    def _handle_ui_error(self, exc_value, exc_traceback):
        """处理UI错误"""
        message = f"界面错误: {exc_value}\n\n请重启应用程序。"
        self._show_error_dialog("界面错误", message)
    
    def _handle_default_error(self, exc_value, exc_traceback):
        """处理默认错误"""
        message = f"应用程序错误: {exc_value}\n\n如果问题持续存在，请联系开发者。"
        self._show_error_dialog("错误", message)
    
    def _show_error_dialog(self, title, message):
        """显示错误对话框"""
        try:
            messagebox.showerror(title, message)
        except:
            print(f"{title}: {message}")
    
    def wrap_function(self, func, error_message=None):
        """包装函数，捕获异常"""
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if error_message:
                    self._show_error_dialog("错误", f"{error_message}: {e}")
                else:
                    exc_type, exc_value, exc_traceback = sys.exc_info()
                    self.handle_exception(exc_type, exc_value, exc_traceback)
                return None
        return wrapper