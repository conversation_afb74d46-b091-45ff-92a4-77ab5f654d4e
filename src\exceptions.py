"""
自定义异常类定义
"""


class BaseAppError(Exception):
    """应用程序基础异常类"""
    def __init__(self, message, details=None):
        super().__init__(message)
        self.details = details
        self.message = message
    
    def __str__(self):
        if self.details:
            return f"{self.message} - {self.details}"
        return self.message


class TempMailAPIError(BaseAppError):
    """tempmail.plus API相关错误"""
    pass


class ConfigurationError(BaseAppError):
    """配置相关错误"""
    pass


class NetworkError(BaseAppError):
    """网络连接错误"""
    pass


class VerificationCodeError(BaseAppError):
    """验证码提取错误"""
    pass


class ValidationError(BaseAppError):
    """数据验证错误"""
    pass


class FileOperationError(BaseAppError):
    """文件操作错误"""
    pass


class WorkflowError(BaseAppError):
    """工作流程错误"""
    pass


class UIError(BaseAppError):
    """用户界面错误"""
    pass