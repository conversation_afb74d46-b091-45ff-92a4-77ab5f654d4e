@echo off
chcp 65001 >nul
echo ========================================
echo Markdown转PDF工具
echo ========================================
echo.

echo 选择转换方式:
echo 1. 使用Python脚本转换
echo 2. 使用Pandoc转换 (需要先安装Pandoc)
echo 3. 打开在线转换器
echo.

set /p choice="请选择 (1-3): "

if "%choice%"=="1" goto python_convert
if "%choice%"=="2" goto pandoc_convert
if "%choice%"=="3" goto online_convert

echo 无效选择，退出...
pause
exit /b 1

:python_convert
echo.
echo 使用Python脚本转换...
python md_to_pdf.py release\README.md release\README.pdf
goto end

:pandoc_convert
echo.
echo 使用Pandoc转换...
pandoc release\README.md -o release\README.pdf --pdf-engine=xelatex -V geometry:margin=1in
if errorlevel 1 (
    echo.
    echo Pandoc转换失败，可能需要安装Pandoc
    echo 下载地址: https://pandoc.org/installing.html
)
goto end

:online_convert
echo.
echo 打开在线转换器...
start https://md-to-pdf.fly.dev/
echo 请手动上传README.md文件进行转换
goto end

:end
echo.
echo 转换完成！
pause
