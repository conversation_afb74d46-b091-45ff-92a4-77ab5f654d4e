#!/usr/bin/env python3
"""
测试简化版应用
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

try:
    from src.app import Application
    print("✓ 成功导入Application类")
    
    # 创建应用实例
    app = Application()
    print("✓ 成功创建Application实例")
    
    # 测试配置加载
    app._load_config()
    print("✓ 成功加载配置")
    
    print("=" * 50)
    print("所有基础功能测试通过！")
    print("应用已准备就绪，只包含邮箱验证码功能")
    print("=" * 50)
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
